{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(claude mcp:*)", "mcp__taskmaster-ai__models", "mcp__brave-search__brave_web_search", "mcp__context7-mcp__resolve-library-id", "mcp__supabase-mcp__list_organizations", "mcp__browser-tools-mcp__takeScreenshot", "mcp__sentry__whoami", "mcp__playwright__playwright_navigate", "mcp__playwright__playwright_close", "mcp__sequential-thinking__sequentialthinking", "Bash(find:*)", "Bash(npm audit:*)", "Bash(ls:*)", "Bash(git checkout:*)", "Bash(npm install:*)", "Bash(npx npm-check-updates:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm test)", "Bash(npm run clean:build:*)", "mcp__playwright__playwright_screenshot", "mcp__playwright__playwright_console_logs", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "mcp__playwright__playwright_get_visible_html", "Bash(npm run clean:*)", "Bash(npm run dev:*)", "Bash(npm run build:*)", "Bash(npm run lint)", "mcp__context7-mcp__get-library-docs", "<PERSON><PERSON>(curl:*)", "Bash(npx tailwindcss:*)", "Bash(./node_modules/.bin/tailwindcss:*)", "Bash(node:*)", "WebFetch(domain:tailwindcss.com)", "mcp__playwright__playwright_evaluate", "Bash(git add:*)", "Bash(grep:*)", "Bash(npm test:*)", "Bash(rm:*)", "Bash(npm cache clean:*)", "Bash(npx tsc:*)", "mcp__playwright__playwright_fill", "mcp__playwright__playwright_get_visible_text", "mcp__playwright__playwright_click", "mcp__playwright__playwright_press_key", "Bash(npm ls:*)", "Bash(git commit:*)", "Bash(git fetch:*)", "Bash(git merge:*)", "Bash(npm run test:*)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(npx playwright:*)", "Bash(echo)", "<PERSON><PERSON>(cat:*)", "mcp__supabase-mcp__list_projects", "mcp__supabase-mcp__execute_sql", "WebFetch(domain:github.com)", "Bash(claude-code settings --help)", "Bash(claude settings --help)", "Bash(npx eslint:*)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:www.airbnb.com)", "WebFetch(domain:www.airbnb.co.uk)", "Bash(npm run audit:seo:*)", "Bash(lighthouse:*)", "Bash(gh workflow:*)", "Bash(cp:*)", "Bash(psql:*)", "<PERSON><PERSON>(time curl:*)", "mcp__atlassian__getAccessibleAtlassianResources", "mcp__atlassian__getVisibleJiraProjects", "mcp__atlassian__createJiraIssue", "mcp__atlassian__atlassianUserInfo", "Bash(NODE_ENV=test npm run build)", "Bash(NODE_ENV=test npm run start)", "mcp__atlassian__addCommentToJiraIssue", "mcp__atlassian__getJiraIssue", "<PERSON><PERSON>(diff:*)", "<PERSON><PERSON>(python3:*)", "Bash(npm:*)", "WebFetch(domain:www.youtube.com)", "mcp__playwright__playwright_get", "WebFetch(domain:main.d3pcuskj59hcq9.amplifyapp.com)", "Bash(PORT=3003 npm run dev)", "mcp__supabase-mcp__list_tables", "WebFetch(domain:stackoverflow.com)", "Bash(./.github/workflows/ci.yml)", "Bash(if grep -r --include=\"*.js\" --include=\"*.ts\" --include=\"*.tsx\" --include=\"*.jsx\" -E \"(https://localhost|yourdomain\\.com|hardcoded.*domain)\" src/)", "Bash(then echo \"❌ DOMAIN CHECK FAILED: Found hardcoded domain references in source code\")", "Bash(exit 1)", "Bash(else echo \"✅ Domain check passed - No hardcoded domains found\")", "Bash(fi)", "mcp__supabase-mcp__list_migrations", "Bash(NODE_ENV=production npm run build)", "Bash(PORT=3001 NODE_ENV=test npm run start)", "Bash(git branch:*)", "Bash(git tag:*)", "Bash(git push:*)", "Bash(NODE_ENV=production NEXT_BUILD_TRACE=false DISABLE_ESLINT_PLUGIN=true npm run build)", "Bash(NODE_ENV=test NEXT_BUILD_TRACE=false DISABLE_ESLINT_PLUGIN=true npm run build)", "WebFetch(domain:docs.aws.amazon.com)", "WebFetch(domain:aws.amazon.com)", "mcp__github__search_repositories", "mcp__browser-tools-mcp__wipeLogs", "mcp__taskmaster-ai__parse_prd", "mcp__taskmaster-ai__add_task", "mcp__serena", "WebFetch(domain:developers.google.com)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["task-master-ai"]}