'use client';

import React, { useState, useRef, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Tag, Store, Clock, ChevronLeft, ChevronRight, PoundSterling } from 'lucide-react';
import Link from 'next/link';
import { ResilientImage } from '@/components/ui/ResilientImage';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "../../../components/ui/dialog";
import { Button } from "../../../components/ui/button";
import { formatPrice } from "../../../lib/utils";
import { formatDate } from '@/app/utils/date';
// Removed ProductStructuredData import - handled at page level to prevent duplication

interface ProductInfoProps {
    product: any; // Accepts Product or TransformedProduct
}

export function ProductInfo({ product }: ProductInfoProps) {
    const transformedProduct = product;
    const [isClaimDetailsExpanded, setIsClaimDetailsExpanded] = useState(false);
    const contentRef = useRef<HTMLDivElement>(null);

    const toggleClaimDetails = () => {
        setIsClaimDetailsExpanded(!isClaimDetailsExpanded);
    };

    // Log detailed promotion info for debugging
    console.log('Product details:', {
        productId: transformedProduct.id,
        productName: transformedProduct.name,
        hasPromotion: !!transformedProduct.promotion,
        promotion: transformedProduct.promotion ? {
            id: transformedProduct.promotion.id,
            purchaseEndDate: transformedProduct.promotion.purchaseEndDate,
            claimStartOffsetDays: transformedProduct.promotion.claimStartOffsetDays,
            claimWindowDays: transformedProduct.promotion.claimWindowDays,
            claimPeriod: transformedProduct.promotion.claimPeriod,
            hasClaimPeriod: !!transformedProduct.promotion.claimPeriod,
            rawPromotion: transformedProduct.promotion
        } : null,
        hasClaimPeriodInRoot: !!transformedProduct.claimPeriod,
        claimPeriodInRoot: transformedProduct.claimPeriod,
        hasPromotionClaimPeriod: !!transformedProduct.promotion?.claimPeriod,
        promotionClaimPeriod: transformedProduct.promotion?.claimPeriod,
        environment: {
            isClient: typeof window !== 'undefined',
            nodeEnv: process.env.NODE_ENV,
            supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL
        }
    });

    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [imageError, setImageError] = useState(false);

    const getImageUrl = (image: string) => {
        if (imageError || !image) {
            return transformedProduct.brand?.logo_url || 
                `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(transformedProduct.name)}`;
        }

        // Define common invalid image path patterns
        const invalidPatterns = [
            'URL_to_image_1',
            'image_url_1.jpg',
            'example.com',
            'blank.html',
            'placeholder-product.png' // Assuming this is a generic placeholder that shouldn't be treated as a real image
        ];

        // Check if the imagePath is invalid
        if (invalidPatterns.some(pattern => image.includes(pattern))) {
            return transformedProduct.brand?.logo_url || 
                `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(transformedProduct.name)}`;
        }

        if (image.startsWith('http')) {
            return image;
        }

        const fullUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${image}`;

        console.log('Image URL:', {
            input: image,
            output: fullUrl,
            supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL
        });

        return fullUrl;
    };


    const productImages = transformedProduct.images || [];
    const hasMultipleImages = productImages.length > 1;

    const currentImage = productImages.length > 0 
        ? productImages[currentImageIndex]
        : (transformedProduct.brand?.logo_url || `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(transformedProduct.name)}`);

    const nextImage = () => {
        if (productImages.length > 0) {
            setCurrentImageIndex((prev) => (prev + 1) % productImages.length);
        }
    };

    const previousImage = () => {
        if (productImages.length > 0) {
            setCurrentImageIndex((prev) => (prev - 1 + productImages.length) % productImages.length);
        }
    };

    console.log('Promotion data:', transformedProduct.promotion);
    console.log('Product data:', transformedProduct);

    const daysUntilEnd = transformedProduct.promotion?.purchaseEndDate
        ? Math.ceil((new Date(transformedProduct.promotion.purchaseEndDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
        : 0;

    // Enhanced data calculations for new pricing design
    const priceRange = useMemo(() => {
        if (!transformedProduct.retailerOffers?.length) return null;
        
        const validPrices = transformedProduct.retailerOffers
            .map((offer: any) => offer.price)
            .filter((price: any): price is number => typeof price === 'number' && price > 0);
            
        if (validPrices.length === 0) return null;
        
        const minPrice = Math.min(...validPrices);
        const maxPrice = Math.max(...validPrices);
        
        return { min: minPrice, max: maxPrice, count: validPrices.length };
    }, [transformedProduct.retailerOffers]);

    const partnersText = useMemo(() => {
        if (!transformedProduct.retailerOffers?.length) return "No partners available";
        
        const firstPartner = transformedProduct.retailerOffers[0]?.retailer?.name;
        const totalPartners = transformedProduct.retailerOffers.length;
        
        if (totalPartners === 1) {
            return `Available at ${firstPartner}`;
        } else {
            return `Available at ${firstPartner} and ${totalPartners - 1} other verified partner${totalPartners > 2 ? 's' : ''}`;
        }
    }, [transformedProduct.retailerOffers]);

    const bestOffer = useMemo(() => {
        if (!transformedProduct.retailerOffers?.length) return null;
        
        const validOffers = transformedProduct.retailerOffers
            .filter((offer: any) => typeof offer.price === 'number' && offer.price > 0);
        
        if (validOffers.length === 0) return null;
        
        return validOffers.reduce((best: any, current: any) => {
            return current.price < best.price ? current : best;
        });
    }, [transformedProduct.retailerOffers]);

    return (
        <>
            {/* ProductStructuredData removed to prevent duplication - handled at page level */}
            
            <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                className="grid md:grid-cols-2 gap-8 mb-12"
            >
                <div className="space-y-4">
                    <div className="aspect-square bg-secondary/10 rounded-lg flex items-center justify-center overflow-hidden relative">
                        <ResilientImage
                            src={getImageUrl(currentImage)}
                            alt={transformedProduct.name}
                            width={600} // Placeholder width
                            height={600} // Placeholder height
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            className="object-contain"
                            onError={(error) => {
                                console.warn(`Product main image error for ${transformedProduct.name}:`, error);
                                // Note: handleImageError expects a React event, but ResilientImage onError provides a string
                                // We'll just log the error here since ResilientImage handles fallbacks internally
                            }}
                            priority
                            productName={transformedProduct.name}
                            brandName={transformedProduct.brand?.name}
                            enableValidation={true}
                            showLoadingState={true}
                            retryOnError={true}
                        />
                        
                        {hasMultipleImages && (
                            <>
                                <Button
                                    variant="secondary"
                                    size="icon"
                                    className="absolute left-2 top-1/2 -translate-y-1/2 z-10"
                                    onClick={previousImage}
                                    aria-label="Previous image"
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant="secondary"
                                    size="icon"
                                    className="absolute right-2 top-1/2 -translate-y-1/2 z-10"
                                    onClick={nextImage}
                                    aria-label="Next image"
                                >
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </>
                        )}

                        <div className="absolute top-4 right-4 bg-secondary text-white px-4 py-2 rounded-lg font-medium">
                            {transformedProduct.cashbackAmount > 0
                                ? `Claim ${formatPrice(transformedProduct.cashbackAmount)} cashback`
                                : 'No Cashback Available'
                            }
                        </div>
                    </div>

                    {hasMultipleImages && (
                        <div className="flex justify-center gap-2 py-10">
                            <div className="flex gap-2 overflow-x-auto max-w-full px-2">
                                {productImages.map((image: string, index: number) => (
                                    <button
                                        key={index}
                                        onClick={() => setCurrentImageIndex(index)}
                                        className={`relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 
                                            ${index === currentImageIndex ? 'ring-2 ring-primary' : ''}`}
                                        aria-label={`View image ${index + 1} of ${productImages.length}`}
                                    >
                                        <ResilientImage
                                            src={getImageUrl(image)}
                                            alt={`${transformedProduct.name} - View ${index + 1}`}
                                            width={80} // Placeholder width
                                            height={80} // Placeholder height
                                            sizes="80px"
                                            className="object-cover"
                                            priority={index === 0}
                                            onError={(error) => {
                                                console.warn(`Product thumbnail error for ${transformedProduct.name} (image ${index + 1}):`, error);
                                                // Note: ResilientImage handles fallbacks internally, no need to call handleImageError
                                            }}
                                            productName={transformedProduct.name}
                                            brandName={transformedProduct.brand?.name}
                                            enableValidation={true}
                                            showLoadingState={false} // Don't show loading state for thumbnails
                                            retryOnError={false} // Don't retry thumbnails to avoid UI jank
                                        />
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                <div>
                    <h1 className="text-3xl font-bold text-primary mb-2">{transformedProduct.name}</h1>
                    {transformedProduct.brand && (
                        <Link href={`/brands/${transformedProduct.brand.id}`} className="inline-block">
                            <p className="text-xl text-foreground/70 mb-4 hover:text-primary transition-colors">
                                {transformedProduct.brand.name}
                            </p>
                        </Link>
                    )}
                    <div className="flex flex-wrap gap-4 mb-6">
                        {transformedProduct.category && (
                            <div className="flex items-center gap-2 text-sm text-foreground/70">
                                <Tag className="h-4 w-4 text-primary" />
                                <span>{transformedProduct.category.name}</span>
                            </div>
                        )}

                        <div className="flex items-center gap-2 text-sm text-foreground/70">
                            <Store className="h-4 w-4 text-primary" />
                            <span>{
                                (transformedProduct.retailerOffers?.length || 0) > 0 
                                    ? `${transformedProduct.retailerOffers.length} Retailers offering cashback`
                                    : 'No retailers available'
                            }</span>
                        </div>
                        {daysUntilEnd !== null && daysUntilEnd > 0 && (
                        <div className="flex items-center gap-2 text-sm text-foreground/70">
                            <Clock className="h-4 w-4 text-primary" />
                            <span>Ends in {daysUntilEnd} days</span>
                        </div>
                    )}
                    </div>

                    {/* ENHANCED Pricing & Offers Section - Improved Visual Hierarchy & Spacing */}
                    <div id="pricing-offers" className="bg-card border-2 border-border rounded-2xl shadow-lg overflow-hidden mb-10">
                        {/* Main Content Container with Enhanced Padding */}
                        <div className="p-8 space-y-8">
                            {/* Availability Header Section - Enhanced with better visual design */}
                            {priceRange && (
                                <div className="bg-gradient-to-br from-primary/8 to-primary/4 border-2 border-primary/25 rounded-2xl p-6 shadow-sm">
                                    <div className="flex items-start gap-4">
                                        <div className="bg-primary/15 p-3 rounded-xl shadow-sm flex-shrink-0">
                                            <Store className="h-6 w-6 text-primary" />
                                        </div>
                                        <div className="flex-1">
                                            <p className="text-sm font-semibold mb-2 text-primary/80 uppercase tracking-wide">
                                                Product Available At
                                            </p>
                                            <p className="text-lg font-bold text-primary leading-relaxed">
                                                {partnersText} from: {formatPrice(priceRange.min)}
                                                {priceRange.min !== priceRange.max && (
                                                    <> - {formatPrice(priceRange.max)}</>
                                                )} - <button 
                                                    onClick={() => {
                                                        document.getElementById('retailer-offers')?.scrollIntoView({ behavior: 'smooth' });
                                                    }}
                                                    className="text-primary hover:text-primary/80 underline decoration-2 underline-offset-2 hover:no-underline transition-all duration-200 font-semibold ml-1"
                                                >
                                                    See More
                                                </button>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Your Savings Section - Enhanced with improved spacing */}
                            {transformedProduct.cashbackAmount && transformedProduct.cashbackAmount > 0 && bestOffer && (
                                <div className="bg-gradient-to-br from-amber-50 via-amber-50/80 to-amber-100/90 border-2 border-amber-200/80 rounded-2xl shadow-md overflow-hidden">
                                    <div className="p-7">
                                        <div className="flex items-center gap-4 mb-7">
                                            <div className="bg-amber-200/70 p-3.5 rounded-xl shadow-sm">
                                                <PoundSterling className="h-7 w-7 text-amber-800" />
                                            </div>
                                            <h3 className="text-2xl font-bold text-amber-900">Your Savings</h3>
                                        </div>
                                        
                                        <div className="space-y-5">
                                            <div className="flex justify-between items-center py-3 border-b border-amber-200/50">
                                                <span className="text-gray-900 font-semibold text-base">{bestOffer.retailer?.name} Price:</span>
                                                <span className="text-2xl font-bold text-gray-900">{formatPrice(bestOffer.price)}</span>
                                            </div>
                                            
                                            <div className="flex justify-between items-center py-3 border-b border-amber-200/50">
                                                <span className="text-gray-900 font-semibold text-base">Cashback from {transformedProduct.brand?.name || 'Brand'}:</span>
                                                <span className="text-2xl font-bold text-emerald-700">-{formatPrice(transformedProduct.cashbackAmount)}</span>
                                            </div>
                                            
                                            <div className="border-t-2 border-amber-300/70 pt-6 mt-6 bg-amber-100/50 -mx-7 px-7 pb-2">
                                                <div className="flex justify-between items-center">
                                                    <span className="text-xl font-bold text-gray-900">Price After Cashback</span>
                                                    <span className="text-3xl font-bold text-emerald-700">
                                                        {formatPrice(bestOffer.price - transformedProduct.cashbackAmount)}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Important Dates Section - Enhanced with better borders and spacing */}
                            {transformedProduct.promotion?.purchaseEndDate && (
                                <div className="bg-gradient-to-br from-muted/40 to-muted/20 border-2 border-border rounded-2xl p-6 shadow-sm">
                                    <h4 className="text-xl font-bold text-center mb-6 text-foreground border-b border-border pb-3">Important Dates</h4>
                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div className="bg-background border-2 border-border rounded-xl p-5 shadow-sm text-center">
                                            <div className="text-sm font-semibold text-muted-foreground mb-3 uppercase tracking-wide">Buy between:</div>
                                            <div className="text-lg font-bold text-foreground leading-relaxed">
                                                Now - {formatDate(transformedProduct.promotion.purchaseEndDate)}
                                            </div>
                                        </div>
                                        {transformedProduct.promotion.claimWindowDays && (
                                            <div className="bg-background border-2 border-border rounded-xl p-5 shadow-sm text-center">
                                                <div className="text-sm font-semibold text-muted-foreground mb-3 uppercase tracking-wide">Claim between:</div>
                                                <div className="text-lg font-bold text-foreground leading-relaxed">
                                                    {(() => {
                                                        const claimStartOffset = transformedProduct.promotion.claimStartOffsetDays || 0;
                                                        const purchaseEndDate = new Date(transformedProduct.promotion.purchaseEndDate);
                                                        const claimStartDate = new Date(purchaseEndDate);
                                                        claimStartDate.setDate(claimStartDate.getDate() + claimStartOffset);
                                                        const claimEndDate = new Date(claimStartDate);
                                                        claimEndDate.setDate(claimEndDate.getDate() + transformedProduct.promotion.claimWindowDays);
                                                        return `${formatDate(claimStartDate.toISOString())} - ${formatDate(claimEndDate.toISOString())}`;
                                                    })()}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Action Buttons Section - ENHANCED spacing and visual hierarchy */}
                        <div className="bg-gradient-to-br from-muted/25 to-muted/10 border-t-2 border-border px-8 py-8">
                            <div className="space-y-6">
                                {/* Primary CTA Button - Enhanced styling */}
                                {transformedProduct.cashbackAmount && transformedProduct.cashbackAmount > 0 && bestOffer?.url && (
                                    <Link 
                                        href={bestOffer.url} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="block"
                                    >
                                        <Button 
                                            className="w-full bg-primary hover:bg-primary/90 text-primary-foreground text-xl font-bold py-8 rounded-2xl shadow-lg border-2 border-primary/30 hover:border-primary/50 transition-all duration-300 hover:shadow-xl transform hover:-translate-y-0.5"
                                        >
                                            Save {formatPrice(transformedProduct.cashbackAmount)} Now
                                        </Button>
                                    </Link>
                                )}
                                
                                {/* Secondary Button - Enhanced with better spacing */}
                                <Button 
                                    variant="secondary"
                                    className="w-full bg-secondary hover:bg-secondary/90 text-secondary-foreground text-xl font-bold py-8 rounded-2xl shadow-md border-2 border-secondary/40 hover:border-secondary/60 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-0.5"
                                    onClick={() => {
                                        document.getElementById('retailer-offers')?.scrollIntoView({ behavior: 'smooth' });
                                    }}
                                >
                                    See All Prices
                                </Button>
                            </div>
                        </div>

                        {/* Expandable Details Section - Enhanced design and spacing */}
                        {transformedProduct.promotion && (
                            <div className="border-t-2 border-border bg-gradient-to-br from-muted/15 to-muted/5">
                                {/* Toggle Button Section - Enhanced padding */}
                                <div className="px-8 py-6 text-center border-b border-border/50">
                                    <button 
                                        onClick={toggleClaimDetails}
                                        className="inline-flex items-center gap-2 text-base font-semibold text-primary hover:text-primary/80 transition-colors px-6 py-3 rounded-xl hover:bg-primary/5 border border-primary/20 hover:border-primary/30"
                                    >
                                        {isClaimDetailsExpanded ? 'Show less' : 'Show more'} claim details
                                    </button>
                                </div>

                                {/* Expandable Claim Details Content - Enhanced spacing */}
                                <div 
                                    className={`overflow-hidden transition-all duration-500 ease-in-out ${
                                        isClaimDetailsExpanded ? 'max-h-[600px] opacity-100' : 'max-h-0 opacity-0'
                                    }`}
                                >
                                    <div 
                                        ref={contentRef}
                                        className="mx-8 mb-8 p-8 bg-background rounded-2xl border-2 border-border shadow-sm"
                                    >
                                        <h4 className="font-bold text-foreground mb-5 text-lg border-b-2 border-border pb-3">Claim Process Details</h4>
                                        <div className="space-y-4">
                                            <div className="flex justify-between items-center py-3 border-b border-border/30">
                                                <span className="text-base font-semibold text-muted-foreground">Purchase by:</span>
                                                <span className="text-base font-bold text-foreground">
                                                    {transformedProduct.promotion.purchaseEndDate ? 
                                                        formatDate(transformedProduct.promotion.purchaseEndDate) : 
                                                        'Not specified'}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center py-3 border-b border-border/30">
                                                <span className="text-base font-semibold text-muted-foreground">Claim starts:</span>
                                                <span className="text-base font-bold text-foreground">
                                                    {transformedProduct.promotion.claimStartOffsetDays != null ? 
                                                        `${transformedProduct.promotion.claimStartOffsetDays} days after purchase` : 
                                                        'Immediately after purchase'}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center py-3">
                                                <span className="text-base font-semibold text-muted-foreground">Claim window:</span>
                                                <span className="text-base font-bold text-foreground">
                                                    {transformedProduct.promotion.claimWindowDays != null ? 
                                                        `${transformedProduct.promotion.claimWindowDays} days` : 
                                                        'Check terms for details'}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Footer Section - Terms & Trust Indicators with enhanced spacing */}
                        <div className="border-t-2 border-border bg-gradient-to-br from-muted/10 to-muted/5 px-8 py-6 space-y-5">
                            {/* Terms & Conditions */}
                            {transformedProduct.promotion?.termsUrl && (
                                <div className="text-center">
                                    <Dialog>
                                        <DialogTrigger asChild>
                                            <button className="text-sm font-medium text-muted-foreground hover:text-primary underline decoration-2 underline-offset-2 hover:no-underline transition-colors px-4 py-2 rounded-lg hover:bg-primary/5 border border-transparent hover:border-primary/20">
                                                View {transformedProduct.promotion.title || 'Cashback'} Terms & Conditions
                                            </button>
                                        </DialogTrigger>
                                        <DialogContent>
                                            <DialogHeader>
                                                <DialogTitle>Cashback Terms & Conditions</DialogTitle>
                                                <DialogDescription className="space-y-2">
                                                    {transformedProduct.promotion.termsDescription || 'Please review the full terms and conditions for this cashback offer.'}
                                                    {transformedProduct.promotion.termsUrl && (
                                                        <Link
                                                            href={transformedProduct.promotion.termsUrl}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            className="inline-block mt-3 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors text-sm font-medium"
                                                        >
                                                            View Full Terms & Conditions
                                                        </Link>
                                                    )}
                                                </DialogDescription>
                                            </DialogHeader>
                                        </DialogContent>
                                    </Dialog>
                                </div>
                            )}

                            {/* Trust Indicators Footer - Enhanced with better border */}
                            <div className="text-center border-t-2 border-border/30 pt-5">
                                <p className="text-sm text-muted-foreground font-semibold flex items-center justify-center gap-3">
                                    <span className="flex items-center gap-1">
                                        🔒 <span>Secure Partner Merchants</span>
                                    </span>
                                    <span className="text-border">•</span>
                                    <span className="flex items-center gap-1">
                                        📄 <span>Receipt-based Rebate System</span>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* No Cashback Available State - Enhanced design with better spacing */}
                    {(!transformedProduct.cashbackAmount || transformedProduct.cashbackAmount <= 0) && (
                        <div className="bg-card border-2 border-border rounded-2xl shadow-lg p-10 text-center mb-10">
                            <div className="space-y-6">
                                <div className="w-20 h-20 bg-muted/50 rounded-2xl flex items-center justify-center mx-auto shadow-sm border border-border">
                                    <Store className="h-10 w-10 text-muted-foreground" />
                                </div>
                                <div className="space-y-3">
                                    <p className="text-xl font-bold text-foreground">No cashback currently available</p>
                                    <p className="text-base text-muted-foreground">This product doesn't have an active cashback promotion</p>
                                </div>
                                {priceRange && (
                                    <div className="pt-6">
                                        <Button 
                                            variant="outline"
                                            className="border-2 border-secondary/40 hover:border-secondary/60 transition-all duration-200 px-10 py-5 text-lg font-semibold rounded-xl"
                                            onClick={() => {
                                                document.getElementById('retailer-offers')?.scrollIntoView({ behavior: 'smooth' });
                                            }}
                                        >
                                            Compare Prices
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                    </div>

                    {/* Detailed Retailer Comparison Section - Enhanced Visual Design */}
                    <div id="retailer-offers" className="mt-12">
                        <div className="bg-card rounded-xl border-2 border-border shadow-lg overflow-hidden">
                            <div className="bg-gradient-to-r from-primary/5 to-secondary/5 px-8 py-6 border-b-2 border-border">
                                <div className="flex items-center gap-3">
                                    <div className="bg-primary/10 p-2 rounded-lg">
                                        <Store className="h-6 w-6 text-primary" />
                                    </div>
                                    <div>
                                        <h3 className="text-xl font-bold text-foreground">Compare Prices</h3>
                                        <p className="text-sm text-muted-foreground">All verified partner retailers</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="p-8">
                                {transformedProduct.retailerOffers?.length > 0 ? (
                                    <div className="space-y-5">
                                        {transformedProduct.retailerOffers
                                            .filter((offer: any) => typeof offer.price === 'number' && offer.price > 0)
                                            .sort((a: any, b: any) => (a.price || 0) - (b.price || 0))
                                            .map((offer: any, index: number) => (
                                                <div key={offer.id || index} className="group flex items-center justify-between p-6 bg-gradient-to-r from-background to-muted/20 border border-border rounded-xl hover:border-primary/20 hover:shadow-md transition-all duration-200">
                                                    <div className="flex items-center gap-4">
                                                        <div className="w-12 h-12 bg-background rounded-xl flex items-center justify-center border-2 border-border group-hover:border-primary/30 transition-colors">
                                                            <Store className="h-6 w-6 text-muted-foreground group-hover:text-primary transition-colors" />
                                                        </div>
                                                        <div>
                                                            <div className="font-semibold text-foreground text-base">{offer.retailer?.name}</div>
                                                            {offer.stockStatus && (
                                                                <div className="text-sm text-muted-foreground bg-muted/50 px-2 py-0.5 rounded-md mt-1 inline-block">
                                                                    {offer.stockStatus}
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                    
                                                    <div className="text-right">
                                                        <div className="font-bold text-xl text-foreground mb-2">
                                                            {formatPrice(offer.price)}
                                                        </div>
                                                        {offer.url ? (
                                                            <Link 
                                                                href={offer.url} 
                                                                target="_blank" 
                                                                rel="noopener noreferrer"
                                                            >
                                                                <Button 
                                                                    variant="outline" 
                                                                    size="sm" 
                                                                    className="border-2 border-primary/20 hover:border-primary/40 hover:bg-primary/5 transition-all duration-200 touch-target-min font-medium"
                                                                >
                                                                    Shop Now
                                                                </Button>
                                                            </Link>
                                                        ) : (
                                                            <Button 
                                                                variant="outline" 
                                                                size="sm" 
                                                                className="border-2 border-muted touch-target-min font-medium"
                                                                disabled
                                                            >
                                                                View Deal
                                                            </Button>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                ) : (
                                    <div className="text-center py-12">
                                        <div className="w-20 h-20 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <Store className="h-10 w-10 text-muted-foreground" />
                                        </div>
                                        <div className="space-y-2">
                                            <p className="text-lg font-medium text-foreground">No retailer offers available</p>
                                            <p className="text-sm text-muted-foreground">Check back later for pricing information</p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </motion.div>
        </>
    );
}