/**
 * =================================================================================
 * DOMAIN CONFIGURATION
 *
 * Centralized configuration for all domain and URL-related constants.
 * This file is the single source of truth for domain management across the app.
 *
 * - `SITE_URL`: The canonical URL for the current environment.
 * - `PRODUCTION_DOMAINS`: Hardcoded production domains for various services.
 * - `CORS_ORIGINS`: Allowed origins for CORS policies.
 * =================================================================================
 */
import { env } from '@/env.mjs';

/**
 * Determines the canonical site URL based on the environment.
 * - In development, it defaults to localhost.
 * - In production/staging, it uses the NEXT_PUBLIC_SITE_URL from environment variables.
 */
const getSiteUrl = (): string => {
  // Always prioritize runtime environment variables over build-time fallbacks
  const runtimeSiteUrl = process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_APP_URL;
  
  if (runtimeSiteUrl && runtimeSiteUrl.trim() !== '') {
    return runtimeSiteUrl;
  }

  // Only use localhost in true development environment
  // Use consistent port to avoid hydration mismatches
  if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_SITE_URL) {
    return 'http://localhost:3001'; // Fixed port for consistency
  }
  
  // Fall back to env.mjs build-time values only if no runtime values
  const buildTimeSiteUrl = env.NEXT_PUBLIC_SITE_URL;
  if (buildTimeSiteUrl && buildTimeSiteUrl.trim() !== '' && !buildTimeSiteUrl.includes('localhost')) {
    return buildTimeSiteUrl;
  }
  
  // Final safety fallback to production domain
  return PRODUCTION_DOMAINS.AWS_AMPLIFY.MAIN;
};

/**
 * The canonical URL for the current environment.
 * Used for sitemaps, structured data, and other absolute URL generation.
 */
export const SITE_URL = getSiteUrl();

/**
 * A collection of hardcoded production domains.
 * These are stable and used for security policies like CORS.
 */
export const PRODUCTION_DOMAINS = {
  AWS_AMPLIFY: {
    CURRENT: 'https://buildopt.d3pcuskj59hcq9.amplifyapp.com/',
    MAIN: 'https://main.d3pcuskj59hcq9.amplifyapp.com',
    STAGING: 'https://staging.d3pcuskj59hcq9.amplifyapp.com',
  },
  CUSTOM: 'https://cashbackdeals.com'
};

/**
 * Allowed origins for Cross-Origin Resource Sharing (CORS).
 * Combines custom domains and Amplify preview domains.
 */
export const CORS_ORIGINS: (string | RegExp)[] = [
  PRODUCTION_DOMAINS.CUSTOM,
  `www.${PRODUCTION_DOMAINS.CUSTOM}`,
  // Also allow development origins
  ...(process.env.NODE_ENV === 'development'
    ? [
        'http://localhost:3003',
        'http://127.0.0.1:3003',
        /^http:\/\/localhost:\d+$/,
        /^http:\/\/127\.0\.0\.1:\d+$/
      ]
    : [])
];

/**
 * Returns the primary canonical domain for the application.
 * Used as a fallback for non-browser requests.
 */
export const getCanonicalDomain = (): string => {
  return PRODUCTION_DOMAINS.CUSTOM;
};
