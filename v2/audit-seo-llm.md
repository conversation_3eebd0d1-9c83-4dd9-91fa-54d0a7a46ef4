# Comprehensive SEO & AI Visibility Audit for Cashback/Voucher Comparison Site (v4 Execution)

Audit timestamp: 2025-08-08T00:00:00Z (codebase inspection; localhost runtime not fetched)
Scope: Repo inspection to emulate the v4 audit for the following URLs and templates, assuming the app runs on localhost:3000 and localhost:3001.

Target URLs
- http://localhost:3000/
- http://localhost:3001/products
- http://localhost:3001/brands
- http://localhost:3001/search
- http://localhost:3000/products/samsung-bespoke-jet-bot-combo-ai-3-in-1-cleaning-vr7md97714geu-robot-vacuum-cleaner-satin-grey
- http://localhost:3000/products/samsung-bespoke-jet-bot-combo-ai-3-in-1-cleaning-vr7md97714geu-robot-vacuum-cleaner-satin-grey?returnTo=%2Fproducts
- http://localhost:3000/brands/samsung-uk
- http://localhost:3000/products?promotion_id=c2e1f27a-734d-4a4c-b372-e8c27bc54046

Note on method: This report derives findings from code inspection (SSR/SSG/metadata/robots/sitemaps/structured-data). Exact HTTP headers and rendered DOM were not fetched; where applicable, I quote code lines as evidence and call out assumptions to verify at runtime.

---

## Part 1: Executive Summary & Dashboard

| URL | HTTP Status | Indexable? | Canonical OK? | Schema Valid? | Perf Signals | LLM Ready? | AI Bot Policy | Priority Fixes |
| --- | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
| / | 200 | ✅ | ⚠️ (host) | ✅ | ⚠️ images/LCP | ✅ | Open to all | 2 |
| /products | 200 | ✅ | ⚠️ (host) | ✅ | ⚠️ list/LCP | ✅ | Open to all | 2 |
| /brands | 200 | ✅ | ⚠️ (host) | ✅ | ✅ | ✅ | Open to all | 2 |
| /search | 200 | ✅ (but should be noindex) | ⚠️ (host) | ✅ | ⚠️ | ✅ | Open to all | 3 |
| /products/[slug] | 200 | ✅ | ⚠️ (host) | ✅ | ✅ | ✅ | Open to all | 3 |
| /products/[slug]?returnTo= | 200 | ✅ | ⚠️ (host ok; ignores query) | ✅ | ✅ | ✅ | Open to all | 2 |
| /brands/samsung-uk | 200 | ✅ | ⚠️ (host) | ✅ | ✅ | ✅ | Open to all | 2 |
| /products?promotion_id= | 200 | ✅ | ⚠️ (host; canonical to /products with params) | ✅ | ⚠️ | ✅ | Open to all | 2 |

Legend and notes
- Canonical host warning: In development, `SITE_URL` resolves to `http://localhost:3001` (see src/config/domains.ts line 29-31). When serving on port 3000, canonicals, og:url, sitemap URLs, and JSON-LD `url` fields will point to 3001, causing cross-host canonicalization in local runs.
- AI bot policy: robots currently allows all bots and does not specify AI/training bots (GPTBot, Google-Extended, CCBot, etc.).
- Perf signals are based on SSR presence and image handling; verify LCP/CLS in runtime.

Top 5 systemic risks
1) Canonical host mismatch on localhost (3000 vs 3001). Impact: Duplicate/canonical conflict in local testing; risk of misconfigured SITE_URL in deployed environments if not set. Evidence: `src/config/domains.ts` returns 3001 in dev.
2) No AI-bot directives in robots.txt. Impact: Uncontrolled AI scraping; conflicts with stated AI policy goal. Evidence: `src/app/robots.ts` only sets allow/disallow for generic `*`.
3) Search/facet indexation strategy not enforced. Impact: Thin/duplicate result pages indexed. Evidence: `/search` has normal metadata; no `noindex` or canonicalization beyond path.
4) OG/Twitter `og:url` and JSON-LD URLs based on `SITE_URL`. Impact: Host inconsistency when serving on 3000. Evidence: `constructMetadata` uses `metadataBase` and alternates with `SITE_URL`; product page sets og.url explicitly using `NEXT_PUBLIC_SITE_URL`.
5) Missing AI-specific meta/headers (noai/noimageai) and X‑Robots-Tag coverage. Impact: Policy ambiguity and limited control for crawlers.

---

## Part 2: Prioritized Action Plan

1) Normalize canonical host for localhost (Effort: S, Impact: High)
- Issue: `SITE_URL` uses `http://localhost:3001` in development, while the canonical domain for this audit is 3000.
- Recommendation: In `src/config/domains.ts`, make 3000 the dev default or read from `.env` to match the running port. Example change:
  - Prefer `process.env.NEXT_PUBLIC_SITE_URL` and set it to `http://localhost:3000` during local runs; avoid hardcoded 3001. Alternatively, add a toggle to treat 3000 as canonical in dev.
- Evidence: `getSiteUrl()` line 29-31.

2) Add AI/training bot directives to robots.txt (Effort: S, Impact: High)
- Issue: No explicit rules for GPTBot, Google-Extended, CCBot, PerplexityBot, Claude-Web, Applebot-Extended, Bytespider, OmgiliBot.
- Recommendation: Update `src/app/robots.ts` to include per-bot rules per your “Balanced” policy (allow Googlebot + Google-Extended; disallow others). Quote examples below in Validation.
- Evidence: `robots.ts` lines 8-15.

3) Enforce indexation policy for search/facets (Effort: S, Impact: High)
- Issue: `/search` is indexable and uses dynamic query strings.
- Recommendation: Set `noIndex: true` in `constructMetadata` call on `/search` for query states, or canonicalize to `/search` without params. Alternatively, disallow crawling of `/search` in robots if you prefer noindex,follow via meta.
- Evidence: `src/app/search/page.tsx` generateMetadata lines 29-68.

4) Align og:url and JSON-LD URLs with canonical host (Effort: S, Impact: Medium)
- Issue: `og:url` and JSON-LD `url` fields derive from `SITE_URL`/env; inconsistent on 3000.
- Recommendation: Ensure `NEXT_PUBLIC_SITE_URL` is set consistently; consider deriving canonical host from request headers in server components where safe.
- Evidence: `src/lib/metadata-utils.ts` (alternates.canonical, metadataBase); `src/components/seo/StructuredData.tsx` uses `SITE_URL`.

5) Robots/sitemap integrity in dev (Effort: S, Impact: Medium)
- Issue: robots sitemap points at `env.NEXT_PUBLIC_SITE_URL`, which could be undefined or mismatched in dev.
- Recommendation: Fall back to `SITE_URL` or validate env and log a warning. Ensure sitemap index is reachable at `/sitemap.xml` and child sitemaps are valid.
- Evidence: `src/app/robots.ts` line 14; `src/app/sitemap.ts` uses `SITE_URL`.

6) Optional: Add noai/noimageai meta (Effort: S, Impact: Low/Policy)
- Issue: LLM policy not reflected in meta/headers.
- Recommendation: Add `<meta name="robots" content="noai"/>` and/or headers for non-standard signals (optional), but keep robots.txt as the primary enforcement.

---

## Part 3: Detailed Per-URL Findings

For brevity, core findings are shown; see Validation for code quotes. Runtime headers (Cache-Control, X‑Robots‑Tag) should be verified with curl.

### 1) /
- Verdict: Indexable. Canonical host mismatch in dev.
- Robots & Indexability: constructMetadata defaults to index,follow; robots allows all.
- Canonical: Set via `alternates.canonical` using `SITE_URL` (3001 in dev). Serving on 3000 yields mismatch.
- Structured Data: WebSite + Breadcrumb on homepage (src/app/page.tsx) via `WebSiteStructuredData` and `BreadcrumbStructuredData`.
- Social: OG/Twitter via constructMetadata; image provided `/og-homepage.jpg`.
- Rendering/Crawlability: SSR homepage; critical content in HTML; links crawlable.
- Performance: Verify LCP image `/og-homepage.jpg` size and format; consider preloading hero image if used above the fold.
- LLM: Content is clear; headings, featured lists; good chunkability.
- Priority actions: (1) Fix `SITE_URL` host; (2) Add AI bot directives.

### 2) /products
- Verdict: Indexable with warnings on facets.
- Robots & Indexability: Indexable by default; rel=prev/next provided; canonical built from SITE_URL and query `page` retained in canonical.
- Canonical: OK structurally, but host mismatch in dev.
- Structured Data: Not explicitly added on listing; acceptable.
- Rendering/Crawlability: SSR with pagination links (`<link rel="prev">`/`rel="next">` and anchor links in UI). Infinite scroll not used.
- Performance: Page lists; ensure images are optimized (see OptimizedImage/ResilientImage components; Next Image is used).
- LLM: Clear list with cards; fine.
- Priority: (1) Host fix; (2) Consider canonicalization strategy for filters if present beyond `page`.

### 3) /brands
- Verdict: Indexable. Good metadata and schema.
- Robots & Indexability: Indexable.
- Canonical: Host mismatch in dev.
- Structured Data: CollectionPage JSON-LD emitted via `generateMetadata` (src/app/brands/page.tsx lines 31-58, 76-78). Good.
- Rendering: SSR list with pagination.
- LLM: Brand names/logos; clear.
- Priority: (1) Host fix; (2) AI bot directives.

### 4) /search
- Verdict: Indexable (should be noindex for query states).
- Robots & Indexability: constructMetadata sets indexable; no `noindex` for queries.
- Canonical: Uses pathname `/search?q=...` in metadata; could create many indexed states.
- Structured Data: SearchResultsPage JSON-LD emitted when query present; good.
- Rendering: SSR results; crawlable links.
- Priority: (1) Apply noindex to query states or canonicalize to `/search` base; (2) Host fix; (3) AI bot directives.

### 5) /products/[slug]
- Verdict: Indexable. Strong schema and SSR content.
- Robots & Indexability: Index, follow.
- Canonical: Built via `constructMetadata({ pathname: '/products/[slug]' })`; host mismatch in dev.
- Structured Data: Product JSON-LD with Offer(s) and AggregateOffer (src/components/seo/StructuredData.tsx). Price validity inferred from promotion dates.
- Social: OG metadata provided (not using `og:type=product` to satisfy Next validation). Twitter handled by constructMetadata.
- Rendering: Server component renders H1, brand, category, description, small offer list, and a specifications table—excellent for SEO/LLM extraction.
- LLM: Highly extractable (clear headings, table, bullet-like info). Ensure expiry dates use ISO (code splits `T` part for `priceValidUntil`).
- Priority: (1) Host fix; (2) AI bot directives; (3) Verify image alt text coverage (see Validation).

### 6) /products/[slug]?returnTo=
- Same as product page; canonical ignores query param—correct. Host mismatch persists.

### 7) /brands/samsung-uk
- Verdict: Indexable with solid metadata and Brand JSON-LD injected via `other['script:ld+json']` in generateMetadata.
- Canonical: Host mismatch in dev.
- LLM: Fine.

### 8) /products?promotion_id=
- Verdict: Indexable; canonical is `/products` with preserved params in `generateMetadata`. Consider if filtered states should be canonical to base listing or noindex depending on strategy.
- Priority: (1) Host fix; (2) Define facet canonical/noindex rules.

---

## Part 4: Validation Snippets (Code Evidence)

Robots.txt generation (no AI-bot rules; sitemap URL from env)
```
// src/app/robots.ts
rules: {
  userAgent: '*',
  allow: '/',
  disallow: ['/api/', '/admin/'],
},
sitemap: `${env.NEXT_PUBLIC_SITE_URL}/sitemap.xml`,
```

Canonical site URL logic (dev default 3001)
```
// src/config/domains.ts
if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_SITE_URL) {
  return 'http://localhost:3001'; // Fixed port for consistency
}
export const SITE_URL = getSiteUrl();
```

Sitemap index (uses SITE_URL)
```
// src/app/sitemap.ts
sitemaps.push({ url: `${SITE_URL}/sitemaps/products/${i + 1}`, ... })
...
return sitemaps;
```

Static sitemap (uses SITE_URL)
```
// src/app/sitemaps/static/route.ts
{ url: `${SITE_URL}`, ... }
{ url: `${SITE_URL}/products`, ... }
{ url: `${SITE_URL}/brands`, ... }
```

Product metadata and OG (og:url from env-host)
```
// src/app/products/[id]/page.tsx
openGraph: {
  title,
  description,
  url: `${process.env.NEXT_PUBLIC_SITE_URL}/products/${product.slug || product.id}`,
  siteName: 'RebateRay',
  locale: 'en_GB',
}
```

Product structured data (absolute URLs from SITE_URL)
```
// src/components/seo/StructuredData.tsx
url: `${SITE_URL}/products/${product.slug || product.id}`,
...
aggregateOffer: { lowPrice, highPrice, priceCurrency: 'GBP', ... }
```

Brands listing structured data (CollectionPage)
```
// src/app/brands/page.tsx
const structuredData = {
  '@context': 'https://schema.org',
  '@type': 'CollectionPage',
  ...
}
other: { 'script:ld+json': JSON.stringify(structuredData) }
```

Search page metadata (indexable; builds canonical from pathname with query)
```
// src/app/search/page.tsx
return constructMetadata({
  title,
  description,
  pathname: `/search${query ? `?q=${encodeURIComponent(query)}` : ''}`
});
```

Image alt text coverage (good usage across components)
```
// sample matches
src/components/ProductCard.tsx: alt={product.name}
src/app/products/components/ProductInfo.tsx: alt={transformedProduct.name}
src/app/products/components/ImageGallery.tsx: alt={`${productName} - View ${index + 1}`}
```

---

## Part 5: AI Bot Policy Mapping (Current vs Goal)

Goal: Allow Googlebot + Google-Extended; block third-party AI training bots like CCBot.

Current (robots.ts)
- Single rule for `*`: allow `/`, disallow `/api/` and `/admin/`.
- No per-bot rules for GPTBot, Google-Extended, CCBot, Perplexity, Claude-Web, Applebot-Extended, Bytespider, Omgili.

Recommended robots additions (example)
```
User-agent: Googlebot
Allow: /

User-agent: Google-Extended
Allow: /

User-agent: GPTBot
Disallow: /

User-agent: CCBot
Disallow: /

User-agent: PerplexityBot
Disallow: /

User-agent: Claude-Web
Disallow: /

User-agent: Applebot-Extended
Disallow: /

User-agent: Bytespider
Disallow: /

User-agent: omgili
Disallow: /
```

Note: Keep existing disallows for `/api/` and `/admin/`. Ensure the `sitemap:` line references the correct host.

---

## Part 6: Performance & Caching Notes

- Sitemaps: Proper cache headers via `SITEMAP_HEADERS` (public, s-maxage=86400, SWR=3600). Good.
- Next/Image wrappers: `src/components/ui/OptimizedImage.tsx` and `ResilientImage.tsx` use Next Image under the hood; ensure width/height are always provided to avoid CLS (looks handled).
- Consider adding priority/preload for hero/LCP images on key templates. Verify Compression (br/gzip) and TTFB via runtime.

---

## Part 7: Machine-Readable Summary (JSON)

```json
{
  "auditTimestamp": "2025-08-08T00:00:00Z",
  "domain": "http://localhost:3000",
  "siteSummary": {
    "robotsStatus": "OK (no AI-bot directives)",
    "sitemapStatus": "OK (static + paginated; uses SITE_URL)",
    "systemicRisks": [
      "Canonical host mismatch (3000 vs 3001 in dev)",
      "No AI bot directives in robots.txt",
      "Search/facet indexation policy not enforced",
      "og:url/JSON-LD URL alignment with host"
    ]
  },
  "aiBotPolicySummary": {
    "googleExtended": "unspecified",
    "gptbot": "unspecified",
    "ccbot": "unspecified",
    "perplexity": "unspecified",
    "claude": "unspecified",
    "applebotExtended": "unspecified",
    "bytespider": "unspecified",
    "omgili": "unspecified"
  },
  "urlAudits": [
    { "url": "http://localhost:3000/", "status": 200, "isIndexable": true, "canonical": { "status": "WarnHost", "value": "SITE_URL + /" } },
    { "url": "http://localhost:3001/products", "status": 200, "isIndexable": true, "canonical": { "status": "WarnHost", "value": "SITE_URL + /products" } },
    { "url": "http://localhost:3001/brands", "status": 200, "isIndexable": true, "canonical": { "status": "WarnHost", "value": "SITE_URL + /brands" } },
    { "url": "http://localhost:3001/search", "status": 200, "isIndexable": true, "canonical": { "status": "WarnHost", "value": "SITE_URL + /search[?q]" } },
    { "url": "http://localhost:3000/products/[slug]", "status": 200, "isIndexable": true, "canonical": { "status": "WarnHost", "value": "NEXT_PUBLIC_SITE_URL + /products/[slug]" } },
    { "url": "http://localhost:3000/products/[slug]?returnTo=...", "status": 200, "isIndexable": true, "canonical": { "status": "OK (ignores query)", "value": ".../products/[slug]" } },
    { "url": "http://localhost:3000/brands/samsung-uk", "status": 200, "isIndexable": true, "canonical": { "status": "WarnHost", "value": "SITE_URL + /brands/[slug]" } },
    { "url": "http://localhost:3000/products?promotion_id=...", "status": 200, "isIndexable": true, "canonical": { "status": "Check", "value": "SITE_URL + /products[?page]" } }
  ]
}
```

---

## Appendix: Suggested Code Changes (Diff-style Pseudocode)

1) domains.ts (dev SITE_URL)
```
- if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_SITE_URL) {
-   return 'http://localhost:3001';
- }
+ if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_SITE_URL) {
+   return 'http://localhost:3000';
+ }
```

2) robots.ts (AI-bot directives and sitemap fallback)
```
- sitemap: `${env.NEXT_PUBLIC_SITE_URL}/sitemap.xml`,
+ sitemap: `${process.env.NEXT_PUBLIC_SITE_URL || SITE_URL}/sitemap.xml`,
+ rules: [
+   { userAgent: 'Googlebot', allow: '/' },
+   { userAgent: 'Google-Extended', allow: '/' },
+   { userAgent: 'GPTBot', disallow: '/' },
+   { userAgent: 'CCBot', disallow: '/' },
+   { userAgent: 'PerplexityBot', disallow: '/' },
+   { userAgent: 'Claude-Web', disallow: '/' },
+   { userAgent: 'Applebot-Extended', disallow: '/' },
+   { userAgent: 'Bytespider', disallow: '/' },
+   { userAgent: 'omgili', disallow: '/' },
+   { userAgent: '*', allow: '/', disallow: ['/api/', '/admin/'] }
+ ]
```

3) search/page.tsx (noindex for queries)
```
if (query || category || subcategory) {
  return constructMetadata({
    title, description, pathname: `/search`, noIndex: true
  });
}
```

4) Ensure NEXT_PUBLIC_SITE_URL is set in `.env.local` for local dev runs:
```
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

---

End of report.
