<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Library V2 - Product Detail Right-Side Components</title>
    <link rel="stylesheet" href="styles/shadcn.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: rgb(var(--background));
            color: rgb(var(--foreground));
            margin: 0;
            padding: 0;
            line-height: 1.5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgb(var(--card));
            border-radius: var(--radius);
            border: 1px solid rgb(var(--border));
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            color: rgb(var(--foreground));
        }
        
        .header p {
            color: rgb(var(--muted-foreground));
            font-size: 1.1rem;
            max-width: 800px;
            margin: 0 auto 0.5rem;
        }
        
        .requirements {
            background: rgb(var(--muted));
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            font-size: 0.9rem;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .component {
            background: rgb(var(--card));
            border: 1px solid rgb(var(--border));
            border-radius: var(--radius);
            padding: 1.5rem;
            position: relative;
        }
        
        .component-header {
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgb(var(--border));
        }
        
        .component-title {
            font-size: 1rem;
            font-weight: 700;
            color: rgb(var(--primary));
            margin-bottom: 0.25rem;
        }
        
        .component-features {
            font-size: 0.75rem;
            color: rgb(var(--muted-foreground));
        }
        
        /* Component-specific styles */
        .cashback-amount {
            font-size: 1.5rem;
            font-weight: 900;
            color: rgb(var(--primary));
        }
        
        .timeline {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .timeline-item {
            text-align: center;
            font-size: 0.8rem;
        }
        
        .timeline-arrow {
            color: rgb(var(--muted-foreground));
        }
        
        .merchant-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .merchant {
            background: rgb(var(--secondary));
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .price-info {
            background: rgb(var(--muted));
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0.5rem 0;
        }
        
        .stars {
            color: #fbbf24;
        }
        
        .cta-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
        
        .urgent {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 0.5rem;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .highlight {
            background: linear-gradient(135deg, rgb(var(--primary)) 0%, rgb(var(--accent)) 100%);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Component Library V2</h1>
            <p>20 Additional Variations for Product Detail Right-Side Components</p>
            <p>Each component sits alongside the feature image and helps users navigate to merchants or explore more product details.</p>
            <div class="requirements">
                <strong>Requirements:</strong> Must include quick merchant exit (1), clear cashback amount (2), buy-by & claim-by dates (3), partner merchant clarity (4). Optional: deeper page navigation (5,6), price ranges (7), reviews (8), product variations (9), category navigation (10), post-cashback prices (11), local delivery (12).
            </div>
        </div>

        <div class="grid">
            <!-- Component 1: Urgency-Focused -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 1: Urgency-Focused</div>
                    <div class="component-features">Features: 1,2,3,4,5 | Emphasizes time-sensitive nature</div>
                </div>
                <div class="urgent">⏰ Limited Time: 15 days left!</div>
                <div class="cashback-amount">15% Cashback</div>
                <div class="timeline">
                    <div class="timeline-item">
                        <strong>Buy by</strong><br>
                        Aug 31, 2024
                    </div>
                    <div class="timeline-arrow">→</div>
                    <div class="timeline-item">
                        <strong>Claim by</strong><br>
                        Sep 30, 2024
                    </div>
                </div>
                <div class="merchant-list">
                    <span class="merchant">🏪 TechWorld</span>
                    <span class="merchant">🏪 ElectroHub</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Shop Now at TechWorld</a>
                    <a href="#" class="btn btn-secondary btn-small">View All Offers</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Submit receipt to brand for rebate
                </p>
            </div>

            <!-- Component 2: Price-After-Cashback Focus -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 2: Price-After-Cashback Focus</div>
                    <div class="component-features">Features: 1,2,3,4,7,11 | Shows final price prominently</div>
                </div>
                <div class="price-info">
                    <div style="font-size: 0.9rem; color: rgb(var(--muted-foreground));">From £899 - £1,199</div>
                    <div class="cashback-amount">£120 Cashback</div>
                    <div style="font-size: 1.2rem; font-weight: 700; color: #059669;">
                        Final Price: £779 - £1,079
                    </div>
                </div>
                <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin: 1rem 0;">
                    <span><strong>Buy by:</strong> Aug 31</span>
                    <span><strong>Claim by:</strong> Sep 30</span>
                </div>
                <div class="merchant-list">
                    <span class="merchant">HomeDepot</span>
                    <span class="merchant">BestBuy</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Get Best Price</a>
                    <a href="#" class="btn btn-secondary btn-small">Compare All Prices</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Partner merchants • Rebate via receipt submission
                </p>
            </div>

            <!-- Component 3: Review-Enhanced -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 3: Review-Enhanced</div>
                    <div class="component-features">Features: 1,2,3,4,6,8 | Includes product rating</div>
                </div>
                <div class="rating">
                    <span class="stars">★★★★☆</span>
                    <span style="font-weight: 600;">4.2</span>
                    <span style="color: rgb(var(--muted-foreground)); font-size: 0.8rem;">(1,247 reviews)</span>
                </div>
                <div class="cashback-amount">10% Back</div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0; font-size: 0.8rem;">
                    <div><strong>Buy by:</strong><br>Aug 31, 2024</div>
                    <div><strong>Claim by:</strong><br>Sep 30, 2024</div>
                </div>
                <div class="merchant-list">
                    <span class="merchant">⭐ Amazon</span>
                    <span class="merchant">⭐ Currys</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Shop at Amazon</a>
                    <a href="#" class="btn btn-secondary btn-small">Read Reviews & Specs</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Trusted partner retailers • Brand rebate program
                </p>
            </div>

            <!-- Component 4: Variation-Aware -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 4: Variation-Aware</div>
                    <div class="component-features">Features: 1,2,3,4,5,9 | Highlights product options</div>
                </div>
                <div style="background: rgb(var(--muted)); padding: 0.75rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="font-size: 0.8rem; color: rgb(var(--muted-foreground)); margin-bottom: 0.25rem;">Available in 5 colors</div>
                    <div style="display: flex; gap: 0.5rem;">
                        <div style="width: 20px; height: 20px; background: #000; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px rgb(var(--border));"></div>
                        <div style="width: 20px; height: 20px; background: #fff; border-radius: 50%; border: 2px solid #000;"></div>
                        <div style="width: 20px; height: 20px; background: #ef4444; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px rgb(var(--border));"></div>
                        <div style="font-size: 0.7rem; align-self: center; color: rgb(var(--muted-foreground));">+2 more</div>
                    </div>
                </div>
                <div class="cashback-amount">8% Cashback</div>
                <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin: 1rem 0;">
                    <span><strong>Buy by:</strong> Aug 31</span>
                    <span><strong>Claim by:</strong> Sep 30</span>
                </div>
                <div class="merchant-list">
                    <span class="merchant">🎨 StyleCo</span>
                    <span class="merchant">🎨 DesignHub</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Choose Color & Buy</a>
                    <a href="#" class="btn btn-secondary btn-small">See All Variations</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Partner retailers • Submit receipt for rebate
                </p>
            </div>

            <!-- Component 5: Local Delivery Focus -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 5: Local Delivery Focus</div>
                    <div class="component-features">Features: 1,2,3,4,12 | Emphasizes local options</div>
                </div>
                <div style="background: #ecfdf5; border: 1px solid #10b981; padding: 0.75rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="font-weight: 600; color: #065f46; font-size: 0.9rem;">🚚 Free Next-Day Delivery</div>
                    <div style="font-size: 0.8rem; color: #047857;">Available in your area</div>
                </div>
                <div class="cashback-amount">12% Back</div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0; font-size: 0.8rem;">
                    <div><strong>Buy by:</strong><br>Aug 31, 2024</div>
                    <div><strong>Claim by:</strong><br>Sep 30, 2024</div>
                </div>
                <div class="merchant-list">
                    <span class="merchant">📍 LocalTech</span>
                    <span class="merchant">📍 CityElectro</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Shop Local Stores</a>
                    <a href="#" class="btn btn-secondary btn-small">Check More Areas</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Local partner stores • Brand rebate program
                </p>
            </div>

            <!-- Component 6: Category Navigation -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 6: Category Navigation</div>
                    <div class="component-features">Features: 1,2,3,4,10 | Links to product category</div>
                </div>
                <div style="background: rgb(var(--muted)); padding: 0.75rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="font-size: 0.8rem; color: rgb(var(--muted-foreground));">Browse similar in</div>
                    <div style="font-weight: 600; color: rgb(var(--primary));">Kitchen Appliances</div>
                </div>
                <div class="cashback-amount">14% Cashback</div>
                <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin: 1rem 0;">
                    <span><strong>Buy by:</strong> Aug 31</span>
                    <span><strong>Claim by:</strong> Sep 30</span>
                </div>
                <div class="merchant-list">
                    <span class="merchant">🏬 KitchenWorld</span>
                    <span class="merchant">🏬 ApplianceHub</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Shop at KitchenWorld</a>
                    <a href="#" class="btn btn-secondary btn-small">Browse Kitchen Appliances</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Partner retailers • Receipt-based rebate
                </p>
            </div>

            <!-- Component 7: Minimal Clean -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 7: Minimal Clean</div>
                    <div class="component-features">Features: 1,2,3,4 | Streamlined essentials only</div>
                </div>
                <div style="text-align: center; padding: 1rem 0;">
                    <div class="cashback-amount" style="font-size: 2rem;">9%</div>
                    <div style="color: rgb(var(--muted-foreground)); font-size: 0.9rem; margin-bottom: 1rem;">Cashback</div>
                    <div style="font-size: 0.8rem; margin-bottom: 1rem;">
                        <strong>Buy by Aug 31</strong> • <strong>Claim by Sep 30</strong>
                    </div>
                    <div style="font-size: 0.8rem; color: rgb(var(--muted-foreground)); margin-bottom: 1rem;">
                        Available at partner merchants
                    </div>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Shop Now</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem; text-align: center;">
                    Submit receipt for rebate
                </p>
            </div>

            <!-- Component 8: Comparison Focused -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 8: Comparison Focused</div>
                    <div class="component-features">Features: 1,2,3,4,5,7 | Emphasizes price comparison</div>
                </div>
                <div style="background: rgb(var(--muted)); padding: 1rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <span style="font-size: 0.8rem;">Best Price:</span>
                        <span style="font-weight: 700;">£649</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-size: 0.8rem;">With Cashback:</span>
                        <span style="font-weight: 700; color: #059669;">£584</span>
                    </div>
                </div>
                <div class="cashback-amount">£65 Back (10%)</div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0; font-size: 0.8rem;">
                    <div><strong>Buy by:</strong><br>Aug 31, 2024</div>
                    <div><strong>Claim by:</strong><br>Sep 30, 2024</div>
                </div>
                <div class="merchant-list">
                    <span class="merchant">💰 BestPrice</span>
                    <span class="merchant">💰 ValueMart</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Get Best Deal</a>
                    <a href="#" class="btn btn-secondary btn-small">Compare All Prices</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Partner merchants • Brand rebate program
                </p>
            </div>

            <!-- Component 9: Trust & Security -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 9: Trust & Security</div>
                    <div class="component-features">Features: 1,2,3,4,6 | Emphasizes trust factors</div>
                </div>
                <div style="background: #f0f9ff; border: 1px solid #0ea5e9; padding: 0.75rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="font-weight: 600; color: #0c4a6e; font-size: 0.9rem;">🛡️ Verified Partners</div>
                    <div style="font-size: 0.8rem; color: #0369a1;">Secure checkout guaranteed</div>
                </div>
                <div class="cashback-amount">11% Cashback</div>
                <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin: 1rem 0;">
                    <span><strong>Buy by:</strong> Aug 31</span>
                    <span><strong>Claim by:</strong> Sep 30</span>
                </div>
                <div class="merchant-list">
                    <span class="merchant">✅ TrustedTech</span>
                    <span class="merchant">✅ SecureShop</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Shop Securely</a>
                    <a href="#" class="btn btn-secondary btn-small">Learn More</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Verified partners • Guaranteed rebate process
                </p>
            </div>

            <!-- Component 10: Mobile-Optimized -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 10: Mobile-Optimized</div>
                    <div class="component-features">Features: 1,2,3,4,5 | Designed for mobile interaction</div>
                </div>
                <div style="display: flex; align-items: center; justify-content: space-between; background: rgb(var(--muted)); padding: 1rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div>
                        <div class="cashback-amount" style="font-size: 1.5rem; margin-bottom: 0;">13%</div>
                        <div style="font-size: 0.7rem; color: rgb(var(--muted-foreground));">CASHBACK</div>
                    </div>
                    <div style="text-align: right; font-size: 0.8rem;">
                        <div><strong>Buy:</strong> Aug 31</div>
                        <div><strong>Claim:</strong> Sep 30</div>
                    </div>
                </div>
                <div class="merchant-list">
                    <span class="merchant">📱 MobileTech</span>
                    <span class="merchant">📱 QuickBuy</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small" style="font-size: 1rem; padding: 0.75rem 1rem;">Tap to Shop</a>
                    <a href="#" class="btn btn-secondary btn-small">More Options</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Partner stores • Easy mobile rebate
                </p>
            </div>

            <!-- Component 11: Savings Calculator -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 11: Savings Calculator</div>
                    <div class="component-features">Features: 1,2,3,4,7,11 | Shows savings breakdown</div>
                </div>
                <div style="background: #fef3c7; border: 1px solid #f59e0b; padding: 1rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="font-weight: 600; color: #92400e; margin-bottom: 0.5rem;">💰 Your Savings</div>
                    <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin-bottom: 0.25rem;">
                        <span>Original Price:</span>
                        <span>£899</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin-bottom: 0.25rem;">
                        <span>Cashback (16%):</span>
                        <span style="color: #059669;">-£144</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-weight: 700; border-top: 1px solid #f59e0b; padding-top: 0.25rem;">
                        <span>You Pay:</span>
                        <span style="color: #059669;">£755</span>
                    </div>
                </div>
                <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin: 1rem 0;">
                    <span><strong>Buy by:</strong> Aug 31</span>
                    <span><strong>Claim by:</strong> Sep 30</span>
                </div>
                <div class="merchant-list">
                    <span class="merchant">💳 PayLess</span>
                    <span class="merchant">💳 SaveMore</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Save £144 Now</a>
                    <a href="#" class="btn btn-secondary btn-small">See All Prices</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Partner merchants • Receipt-based rebate
                </p>
            </div>

            <!-- Component 12: Stock Alert -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 12: Stock Alert</div>
                    <div class="component-features">Features: 1,2,3,4,5 | Includes stock information</div>
                </div>
                <div style="background: #fef2f2; border: 1px solid #ef4444; padding: 0.75rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="font-weight: 600; color: #dc2626; font-size: 0.9rem;">⚠️ Limited Stock</div>
                    <div style="font-size: 0.8rem; color: #b91c1c;">Only 3 left at this price</div>
                </div>
                <div class="cashback-amount">18% Cashback</div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0; font-size: 0.8rem;">
                    <div><strong>Buy by:</strong><br>Aug 31, 2024</div>
                    <div><strong>Claim by:</strong><br>Sep 30, 2024</div>
                </div>
                <div class="merchant-list">
                    <span class="merchant">📦 StockMart</span>
                    <span class="merchant">📦 QuickStock</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Buy Before It's Gone</a>
                    <a href="#" class="btn btn-secondary btn-small">Check Stock</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Partner retailers • Brand rebate program
                </p>
            </div>

            <!-- Component 13: Social Proof -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 13: Social Proof</div>
                    <div class="component-features">Features: 1,2,3,4,6,8 | Includes social validation</div>
                </div>
                <div style="background: rgb(var(--muted)); padding: 0.75rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <span class="stars">★★★★★</span>
                        <span style="font-weight: 600;">4.8</span>
                        <span style="color: rgb(var(--muted-foreground)); font-size: 0.8rem;">(2,341 reviews)</span>
                    </div>
                    <div style="font-size: 0.8rem; color: rgb(var(--muted-foreground));">
                        "Excellent value with cashback!" - Sarah M.
                    </div>
                </div>
                <div class="cashback-amount">7% Cashback</div>
                <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin: 1rem 0;">
                    <span><strong>Buy by:</strong> Aug 31</span>
                    <span><strong>Claim by:</strong> Sep 30</span>
                </div>
                <div class="merchant-list">
                    <span class="merchant">⭐ TopRated</span>
                    <span class="merchant">⭐ BestReviews</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Join 2,341 Happy Buyers</a>
                    <a href="#" class="btn btn-secondary btn-small">Read All Reviews</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Trusted partners • Verified rebate process
                </p>
            </div>

            <!-- Component 14: Bundle Offer -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 14: Bundle Offer</div>
                    <div class="component-features">Features: 1,2,3,4,6,9 | Suggests related products</div>
                </div>
                <div style="background: #f0fdf4; border: 1px solid #22c55e; padding: 0.75rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="font-weight: 600; color: #15803d; font-size: 0.9rem;">🎁 Bundle & Save More</div>
                    <div style="font-size: 0.8rem; color: #166534;">Add accessories for extra 5% cashback</div>
                </div>
                <div class="cashback-amount">20% Total Cashback</div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0; font-size: 0.8rem;">
                    <div><strong>Buy by:</strong><br>Aug 31, 2024</div>
                    <div><strong>Claim by:</strong><br>Sep 30, 2024</div>
                </div>
                <div class="merchant-list">
                    <span class="merchant">🎁 BundleDeals</span>
                    <span class="merchant">🎁 ComboSaver</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Shop Bundle Deal</a>
                    <a href="#" class="btn btn-secondary btn-small">See Bundle Options</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Partner stores • Enhanced rebate for bundles
                </p>
            </div>

            <!-- Component 15: Personalized -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 15: Personalized</div>
                    <div class="component-features">Features: 1,2,3,4,5,12 | Tailored to user preferences</div>
                </div>
                <div style="background: #faf5ff; border: 1px solid #a855f7; padding: 0.75rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="font-weight: 600; color: #7c3aed; font-size: 0.9rem;">✨ Recommended for You</div>
                    <div style="font-size: 0.8rem; color: #6d28d9;">Based on your recent searches</div>
                </div>
                <div class="cashback-amount">15% Cashback</div>
                <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin: 1rem 0;">
                    <span><strong>Buy by:</strong> Aug 31</span>
                    <span><strong>Claim by:</strong> Sep 30</span>
                </div>
                <div class="merchant-list">
                    <span class="merchant">🎯 PersonalPicks</span>
                    <span class="merchant">🎯 SmartChoice</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Shop My Pick</a>
                    <a href="#" class="btn btn-secondary btn-small">See More Recommendations</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Curated partners • Personalized rebate offers
                </p>
            </div>

            <!-- Component 16: Gamified -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 16: Gamified</div>
                    <div class="component-features">Features: 1,2,3,4,5 | Adds gamification elements</div>
                </div>
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <span style="font-size: 1.2rem;">🏆</span>
                        <span style="font-weight: 600;">Level Up Your Savings!</span>
                    </div>
                    <div style="font-size: 0.8rem; opacity: 0.9;">
                        Unlock 17% cashback - Premium tier reward
                    </div>
                </div>
                <div class="cashback-amount">17% Cashback</div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0; font-size: 0.8rem;">
                    <div><strong>Buy by:</strong><br>Aug 31, 2024</div>
                    <div><strong>Claim by:</strong><br>Sep 30, 2024</div>
                </div>
                <div class="merchant-list">
                    <span class="merchant">🎮 GameDeals</span>
                    <span class="merchant">🎮 RewardHub</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Claim Reward</a>
                    <a href="#" class="btn btn-secondary btn-small">View Progress</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Gaming partners • Achievement-based rebates
                </p>
            </div>

            <!-- Component 17: Eco-Friendly -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 17: Eco-Friendly</div>
                    <div class="component-features">Features: 1,2,3,4,6 | Highlights sustainability</div>
                </div>
                <div style="background: #f0fdf4; border: 1px solid #16a34a; padding: 0.75rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="font-weight: 600; color: #15803d; font-size: 0.9rem;">🌱 Eco-Friendly Choice</div>
                    <div style="font-size: 0.8rem; color: #166534;">Carbon-neutral shipping available</div>
                </div>
                <div class="cashback-amount">12% Green Cashback</div>
                <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin: 1rem 0;">
                    <span><strong>Buy by:</strong> Aug 31</span>
                    <span><strong>Claim by:</strong> Sep 30</span>
                </div>
                <div class="merchant-list">
                    <span class="merchant">🌿 EcoStore</span>
                    <span class="merchant">🌿 GreenChoice</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Shop Sustainably</a>
                    <a href="#" class="btn btn-secondary btn-small">Learn About Impact</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Sustainable partners • Eco-conscious rebates
                </p>
            </div>

            <!-- Component 18: Premium Exclusive -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 18: Premium Exclusive</div>
                    <div class="component-features">Features: 1,2,3,4,5,8 | VIP treatment focus</div>
                </div>
                <div style="background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); color: #92400e; padding: 1rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <span style="font-size: 1.2rem;">👑</span>
                        <span style="font-weight: 600;">Premium Member Exclusive</span>
                    </div>
                    <div style="font-size: 0.8rem; opacity: 0.9;">
                        Enhanced 22% cashback + priority support
                    </div>
                </div>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span style="font-weight: 600;">4.9</span>
                    <span style="color: rgb(var(--muted-foreground)); font-size: 0.8rem;">(Premium verified)</span>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0; font-size: 0.8rem;">
                    <div><strong>Buy by:</strong><br>Aug 31, 2024</div>
                    <div><strong>Claim by:</strong><br>Sep 30, 2024</div>
                </div>
                <div class="merchant-list">
                    <span class="merchant">👑 PremiumDeals</span>
                    <span class="merchant">👑 VIPStore</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Access Premium Deal</a>
                    <a href="#" class="btn btn-secondary btn-small">Upgrade Membership</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    Premium partners • VIP rebate processing
                </p>
            </div>

            <!-- Component 19: Multi-Currency -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 19: Multi-Currency</div>
                    <div class="component-features">Features: 1,2,3,4,7,11 | International focus</div>
                </div>
                <div style="background: rgb(var(--muted)); padding: 0.75rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="font-size: 0.8rem; color: rgb(var(--muted-foreground)); margin-bottom: 0.5rem;">Available in multiple currencies</div>
                    <div style="display: flex; gap: 1rem; font-size: 0.8rem;">
                        <span>£899 (GBP)</span>
                        <span>€1,049 (EUR)</span>
                        <span>$1,199 (USD)</span>
                    </div>
                </div>
                <div class="cashback-amount">13% Cashback</div>
                <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin: 1rem 0;">
                    <span><strong>Buy by:</strong> Aug 31</span>
                    <span><strong>Claim by:</strong> Sep 30</span>
                </div>
                <div class="merchant-list">
                    <span class="merchant">🌍 GlobalMart</span>
                    <span class="merchant">🌍 WorldWide</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Shop in Your Currency</a>
                    <a href="#" class="btn btn-secondary btn-small">Compare Global Prices</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    International partners • Multi-currency rebates
                </p>
            </div>

            <!-- Component 20: AI-Powered -->
            <div class="component">
                <div class="component-header">
                    <div class="component-title">Variant 20: AI-Powered</div>
                    <div class="component-features">Features: 1,2,3,4,5,6 | Smart recommendations</div>
                </div>
                <div style="background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%); color: white; padding: 1rem; border-radius: 6px; margin-bottom: 1rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <span style="font-size: 1.2rem;">🤖</span>
                        <span style="font-weight: 600;">AI Smart Match</span>
                    </div>
                    <div style="font-size: 0.8rem; opacity: 0.9;">
                        95% compatibility with your preferences
                    </div>
                </div>
                <div class="cashback-amount">19% AI-Optimized</div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0; font-size: 0.8rem;">
                    <div><strong>Buy by:</strong><br>Aug 31, 2024</div>
                    <div><strong>Claim by:</strong><br>Sep 30, 2024</div>
                </div>
                <div class="merchant-list">
                    <span class="merchant">🤖 SmartDeals</span>
                    <span class="merchant">🤖 AIStore</span>
                </div>
                <div class="cta-group">
                    <a href="#" class="btn btn-primary btn-small">Get AI Recommendation</a>
                    <a href="#" class="btn btn-secondary btn-small">See Smart Analysis</a>
                </div>
                <p style="font-size: 0.75rem; color: rgb(var(--muted-foreground)); margin-top: 0.5rem;">
                    AI-powered partners • Intelligent rebate optimization
                </p>
            </div>
        </div>

        <div style="background: rgb(var(--card)); border: 1px solid rgb(var(--border)); border-radius: var(--radius); padding: 2rem; margin-top: 3rem; text-align: center;">
            <h2 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem; color: rgb(var(--foreground));">Component Library Complete</h2>
            <p style="color: rgb(var(--muted-foreground)); margin-bottom: 1rem;">
                20 unique variations of right-side components for product detail pages. Each component includes the 4 must-have features and 3-7 total features from the requirements list.
            </p>
            <p style="font-size: 0.9rem; color: rgb(var(--muted-foreground));">
                All components are designed to work alongside the feature image and help users either navigate to merchant sites or explore deeper product information.
            </p>
        </div>
    </div>
</body>
</html>