<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Samsung Bespoke SpaceMax RL38C776ASR – Smart Combi Fridge Freezer | RebateRay</title>
  <meta name="description" content="Read the main features, technical and physical specs, plus additional info for Samsung Bespoke SpaceMax RL38C776ASR smart combi fridge freezer. Optimized for mobile scanning and SEO." />

  <!-- Standalone Tailwind via CDN (no project changes) -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: { DEFAULT: '#3A7BD5' },
            accent: { DEFAULT: '#FAB005' },
          },
          borderRadius: { xl2: '1.25rem' },
          boxShadow: { soft: '0 10px 30px rgba(2,8,20,0.08)' },
        }
      }
    }
  </script>

  <link rel="stylesheet" href="./styles/tokens.css" />
  <link rel="stylesheet" href="./styles/shadcn.css" />
  <link rel="stylesheet" href="./styles/product.css" />
</head>
<body class="bg-[rgb(var(--background))] text-[rgb(var(--foreground))]">
  <header class="sticky top-0 z-40 bg-white/80 backdrop-blur border-b border-slate-200">
    <div class="max-w-6xl mx-auto px-4 py-3 flex items-center justify-between">
      <a href="#" class="font-black text-slate-900 text-lg flex items-center gap-2"><span class="inline-block w-6 h-6 rounded-md bg-gradient-to-br from-sky-500 to-indigo-600"></span> RebateRay</a>
      <nav class="hidden sm:flex items-center gap-4 text-sm">
        <a href="#features" class="text-slate-600 hover:text-slate-900">Features</a>
        <a href="#tech-specs" class="text-slate-600 hover:text-slate-900">Tech Specs</a>
        <a href="#physical-specs" class="text-slate-600 hover:text-slate-900">Physical</a>
        <a href="#additional" class="text-slate-600 hover:text-slate-900">More</a>
      </nav>
    </div>
  </header>

  <!-- Product header -->
  <section class="bg-gradient-to-b from-slate-50 to-white">
    <div class="max-w-6xl mx-auto px-4 py-8 grid md:grid-cols-2 gap-8">
      <div class="space-y-4">
        <div class="inline-flex items-center gap-2"><span class="badge">Bespoke</span><span class="badge">SpaceMax</span><span class="badge">SmartThings</span></div>
        <h1 class="text-3xl/tight md:text-4xl/tight font-black">Samsung Bespoke SpaceMax RL38C776ASR Smart Combi Fridge Freezer – Real Steel</h1>
        <p class="text-slate-600">Large-capacity, energy-efficient fridge freezer with SmartThings connectivity, All-Around Cooling, and premium Bespoke finish.</p>
      </div>
      <div class="bg-white rounded-2xl shadow-soft p-4 border border-slate-200">
        <img alt="Samsung RL38C776ASR" src="https://images.unsplash.com/photo-1556909212-d5b604d0c8f9?q=80&w=1200&auto=format&fit=crop" class="rounded-xl" />
      </div>
    </div>
  </section>

  <!-- Price comparison demo (placeholder) -->
  <section class="max-w-6xl mx-auto px-4 py-6" id="price">
    <h2 class="text-xl font-extrabold mb-4">Price Comparison</h2>
    <div class="price-compare card p-4">
      <div class="vendor"><div class="font-semibold">ElectroMart</div><div class="text-slate-600">£899.00</div><a class="btn btn-primary" href="#">Go to retailer</a></div>
      <div class="vendor"><div class="font-semibold">ApplianceHub</div><div class="text-slate-600">£879.00</div><a class="btn btn-secondary" href="#">Go to retailer</a></div>
      <div class="vendor"><div class="font-semibold">MegaStore</div><div class="text-slate-600">£905.00</div><a class="btn btn-secondary" href="#">Go to retailer</a></div>
    </div>
  </section>

  <!-- Sticky tabs after price comparison -->
  <nav class="section-tabs" aria-label="Product sections">
    <div class="max-w-6xl mx-auto rail">
      <a href="#features" class="tab active">Main Features</a>
      <a href="#tech-specs" class="tab">Technical Specs</a>
      <a href="#physical-specs" class="tab">Physical Specs</a>
      <a href="#additional" class="tab">Additional Info</a>
    </div>
  </nav>

  <!-- Main Features -->
  <section id="features" class="anchor">
    <div class="max-w-6xl mx-auto px-4 py-8 grid lg:grid-cols-3 gap-6">
      <article class="card p-5 card-hover lg:col-span-2">
        <h2 class="text-2xl font-black mb-3">Main Features</h2>
        <ul class="grid sm:grid-cols-2 gap-3 text-[15px]">
          <li class="feature-badge">SpaceMax Technology – bigger inside, same outside</li>
          <li class="feature-badge">All-Around Cooling for consistent temperature</li>
          <li class="feature-badge">No Frost – saves time, maintains efficiency</li>
          <li class="feature-badge">SmartThings Wi‑Fi control and diagnostics</li>
          <li class="feature-badge">Power Cool/Freeze for rapid chilling</li>
          <li class="feature-badge">Metal Cooling plate retains cold air</li>
        </ul>
      </article>
      <aside class="card p-5 card-hover">
        <h3 class="font-extrabold mb-2">Good to know</h3>
        <ul class="list-disc pl-5 text-sm text-slate-600 space-y-1">
          <li>Reversible door – flexible kitchen layouts</li>
          <li>Door alarm – helps avoid temperature rises</li>
          <li>LED lighting – bright, energy‑efficient</li>
        </ul>
      </aside>
    </div>
  </section>

  <!-- Technical Specifications -->
  <section id="tech-specs" class="anchor">
    <div class="max-w-6xl mx-auto px-4 py-8 grid lg:grid-cols-3 gap-6">
      <article class="card p-5 card-hover lg:col-span-2">
        <h2 class="text-2xl font-black mb-4">Technical Specifications</h2>
        <div class="specs">
          <div class="row"><div class="cell key">Energy Rating</div><div class="cell">C</div></div>
          <div class="row"><div class="cell key">Total Capacity</div><div class="cell">387 L (Refrigerator 273 L, Freezer 114 L)</div></div>
          <div class="row"><div class="cell key">Noise Level</div><div class="cell">35 dB</div></div>
          <div class="row"><div class="cell key">Annual Energy Consumption</div><div class="cell">169 kWh</div></div>
          <div class="row"><div class="cell key">Cooling System</div><div class="cell">Twin Cooling Plus, All‑Around Cooling</div></div>
          <div class="row"><div class="cell key">Connectivity</div><div class="cell">Wi‑Fi (SmartThings), iOS & Android apps</div></div>
          <div class="row"><div class="cell key">Display</div><div class="cell">External LED display</div></div>
        </div>
      </article>
      <aside class="card p-5 card-hover">
        <h3 class="font-extrabold mb-2">In the box</h3>
        <ul class="list-disc pl-5 text-sm text-slate-600 space-y-1">
          <li>Fridge Freezer RL38C776ASR</li>
          <li>Warranty & Quick Start</li>
          <li>Egg Tray + Ice Tray</li>
        </ul>
      </aside>
    </div>
  </section>

  <!-- Physical Specifications -->
  <section id="physical-specs" class="anchor">
    <div class="max-w-6xl mx-auto px-4 py-8 grid lg:grid-cols-3 gap-6">
      <article class="card p-5 card-hover lg:col-span-2">
        <h2 class="text-2xl font-black mb-4">Physical Specifications</h2>
        <div class="specs">
          <div class="row"><div class="cell key">Dimensions (H×W×D)</div><div class="cell">2030 × 595 × 658 mm</div></div>
          <div class="row"><div class="cell key">Weight</div><div class="cell">Approx. 76 kg</div></div>
          <div class="row"><div class="cell key">Finish</div><div class="cell">Real Stainless Steel (Bespoke)</div></div>
          <div class="row"><div class="cell key">Door Hinge</div><div class="cell">Right (reversible)</div></div>
          <div class="row"><div class="cell key">Refrigerant</div><div class="cell">R600a</div></div>
        </div>
      </article>
      <aside class="card p-5 card-hover">
        <h3 class="font-extrabold mb-2">Installation</h3>
        <ul class="list-disc pl-5 text-sm text-slate-600 space-y-1">
          <li>Requires 10 mm rear clearance</li>
          <li>Standard UK plug</li>
          <li>Level floors recommended</li>
        </ul>
      </aside>
    </div>
  </section>

  <!-- Additional Information -->
  <section id="additional" class="anchor">
    <div class="max-w-6xl mx-auto px-4 py-8 grid lg:grid-cols-3 gap-6">
      <article class="card p-5 card-hover lg:col-span-2">
        <h2 class="text-2xl font-black mb-4">Additional Information</h2>
        <div class="prose space-y-3">
          <p>Backed by Samsung's SmartThings ecosystem. Remote diagnostics, energy monitoring, and mode switching make it simple to optimize efficiency and troubleshoot issues quickly.</p>
          <p>SpaceMax insulation increases internal capacity without increasing external dimensions. Paired with All‑Around Cooling and Metal Cooling, food stays fresher for longer.</p>
          <p>Ideal for medium to large households that need flexible storage, fast chilling, and contemporary styling.</p>
        </div>
      </article>
      <aside class="card p-5 card-hover">
        <h3 class="font-extrabold mb-2">Warranty & Support</h3>
        <ul class="list-disc pl-5 text-sm text-slate-600 space-y-1">
          <li>2‑year manufacturer's warranty</li>
          <li>10‑year compressor warranty</li>
          <li>Samsung UK support & service network</li>
        </ul>
      </aside>
    </div>
  </section>

  <!-- SEO Structured Data (Product) -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "Samsung Bespoke SpaceMax RL38C776ASR Smart Combi Fridge Freezer",
    "brand": { "@type": "Brand", "name": "Samsung" },
    "model": "RL38C776ASR/EU",
    "category": "Appliances > Refrigerators",
    "image": [
      "https://images.unsplash.com/photo-1556909212-d5b604d0c8f9?q=80&w=1200&auto=format&fit=crop"
    ],
    "offers": {
      "@type": "AggregateOffer",
      "priceCurrency": "GBP",
      "lowPrice": "879.00",
      "highPrice": "905.00",
      "offerCount": "3"
    },
    "additionalProperty": [
      { "@type": "PropertyValue", "name": "Energy Rating", "value": "C" },
      { "@type": "PropertyValue", "name": "Total Capacity", "value": "387 L" },
      { "@type": "PropertyValue", "name": "Noise Level", "value": "35 dB" }
    ]
  }
  </script>

  <!-- Micro JS to highlight active tab on scroll -->
  <script>
    const links = document.querySelectorAll('.section-tabs .tab');
    const sections = Array.from(['features','tech-specs','physical-specs','additional']).map(id => document.getElementById(id));
    const onScroll = () => {
      const pos = window.scrollY + 90;
      let activeId = 'features';
      for (const sec of sections) { if (sec.offsetTop <= pos) activeId = sec.id; }
      links.forEach(a => a.classList.toggle('active', a.getAttribute('href') === '#' + activeId));
    };
    document.addEventListener('scroll', onScroll, { passive: true });
    onScroll();
  </script>
</body>
</html>
