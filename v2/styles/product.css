/* Product detail enhancements specific styles (works with Tailwind + tokens + shadcn.css) */

/******** Tabs (sticky on mobile) ********/
.section-tabs {
  position: sticky;
  top: 0;
  z-index: 30;
  background: rgba(255,255,255,0.85);
  backdrop-filter: saturate(180%) blur(10px);
  border-bottom: 1px solid rgb(229 231 235);
}
@media (prefers-color-scheme: dark) {
  .section-tabs { background: rgba(17,20,26,0.7); border-bottom-color: rgba(255,255,255,0.08); }
}
.section-tabs .rail {
  display: flex;
  gap: .5rem;
  overflow-x: auto;
  padding: .5rem .75rem;
}
.section-tabs .tab {
  white-space: nowrap;
  border-radius: 999px;
  padding: .35rem .75rem;
  font-weight: 700;
  border: 1px solid rgb(229 231 235);
}
.section-tabs .tab.active { background: rgb(var(--primary)); color: #fff; border-color: transparent; }

/******** Feature badges ********/
.feature-badge { display: inline-flex; align-items: center; gap: .5rem; padding: .4rem .6rem; border: 1px solid rgb(229 231 235); border-radius: 999px; background: rgb(244 244 245); font-weight: 700; font-size: .8rem; }
@media (prefers-color-scheme: dark) {
  .feature-badge { background: rgba(255,255,255,0.06); border-color: rgba(255,255,255,0.12); color: rgba(255,255,255,0.9); }
}

/******** Spec table ********/
.specs { border: 1px solid rgb(229 231 235); border-radius: 14px; overflow: hidden; }
.specs .row { display: grid; grid-template-columns: 1fr 2fr; border-bottom: 1px solid rgb(229 231 235); }
.specs .row:last-child { border-bottom: 0; }
.specs .cell { padding: .85rem 1rem; }
.specs .key { background: rgb(249 250 251); font-weight: 700; color: rgb(55 65 81); }
@media (max-width: 640px) {
  .specs .row { grid-template-columns: 1fr; }
  .specs .key { background: rgb(255 255 255); color: rgb(75 85 99); }
}
@media (prefers-color-scheme: dark) {
  .specs { border-color: rgba(255,255,255,0.12); }
  .specs .row { border-bottom-color: rgba(255,255,255,0.12); }
  .specs .key { background: rgba(255,255,255,0.04); color: rgba(255,255,255,0.8); }
}

/******** Price comparison demo ********/
.price-compare .vendor { display: grid; grid-template-columns: 2fr 1fr 1fr; align-items: center; gap: .75rem; padding: .75rem 0; border-bottom: 1px dashed rgb(229 231 235); }
.price-compare .vendor:last-child { border-bottom: 0; }
@media (max-width: 640px) { .price-compare .vendor { grid-template-columns: 1fr; } }

/******** Micro-interactions ********/
.card-hover { transition: transform .15s ease, box-shadow .2s ease; }
.card-hover:hover { transform: translateY(-2px); box-shadow: 0 8px 24px rgba(2,8,20,.08); }

/******** Icons ********/
.icon { width: 20px; height: 20px; display: inline-block; vertical-align: middle; }

/******** Anchor headings ********/
.anchor { scroll-margin-top: 72px; }
