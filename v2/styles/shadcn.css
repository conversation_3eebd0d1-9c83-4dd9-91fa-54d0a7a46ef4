/* Minimal shadcn-like tokens and primitives for standalone demos */
:root {
  --background: 252 252 253;
  --foreground: 17 24 39;
  --card: 255 255 255;
  --card-foreground: 17 24 39;
  --popover: 255 255 255;
  --popover-foreground: 17 24 39;
  --primary: 58 123 213; /* azure */
  --primary-foreground: 255 255 255;
  --secondary: 244 244 245;
  --secondary-foreground: 17 24 39;
  --muted: 244 244 245;
  --muted-foreground: 107 114 128;
  --accent: 250 176 5;
  --accent-foreground: 17 24 39;
  --destructive: 239 68 68;
  --destructive-foreground: 255 255 255;
  --border: 229 231 235;
  --input: 229 231 235;
  --ring: 58 123 213;
  --radius: 12px;
}

* { border-color: rgb(var(--border)); }

.card { background: rgb(var(--card)); color: rgb(var(--card-foreground)); border: 1px solid rgb(var(--border)); border-radius: var(--radius); }
.btn { display: inline-flex; align-items: center; justify-content: center; gap: .5rem; font-weight: 600; border-radius: calc(var(--radius) + 999px); border: 1px solid transparent; padding: .625rem .9rem; }
.btn-primary { background: rgb(var(--primary)); color: rgb(var(--primary-foreground)); }
.btn-secondary { background: rgb(var(--secondary)); color: rgb(var(--secondary-foreground)); border-color: rgb(var(--border)); }
.badge { display: inline-flex; align-items: center; gap: .4rem; font-size: .75rem; padding: .3rem .55rem; border-radius: 999px; border: 1px solid rgb(var(--border)); background: rgb(var(--muted)); color: rgb(var(--muted-foreground)); font-weight: 700; }
.input { display: inline-flex; align-items: center; gap: .5rem; border: 1px solid rgb(var(--border)); border-radius: 10px; padding: .625rem .8rem; background: rgb(var(--popover)); }
.input input { outline: none; border: 0; background: transparent; width: 100%; }

.table-wrap { overflow: auto; border: 1px solid rgb(var(--border)); border-radius: var(--radius); }
.table { width: 100%; border-collapse: collapse; }
.table th, .table td { padding: .9rem .9rem; border-bottom: 1px solid rgb(var(--border)); text-align: left; }
.table tr:last-child td { border-bottom: 0; }

.kbd { border-radius: 6px; border: 1px solid rgb(var(--border)); background: rgb(var(--muted)); color: rgb(var(--muted-foreground)); padding: 0 .45rem; font-size: .75rem; }

.prose { color: rgb(var(--foreground)); }
.prose h2 { font-size: clamp(22px, 4.5vw, 30px); font-weight: 900; }
.prose h3 { font-size: clamp(18px, 4vw, 22px); font-weight: 800; }
.prose p { color: rgb(var(--muted-foreground)); }
