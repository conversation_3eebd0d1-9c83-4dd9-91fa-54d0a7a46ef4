/* Design System Tokens and Base Styles */
:root {
  /* Brand colors */
  --color-primary: 255 70 85;        /* Coral Red */
  --color-secondary: 58 123 213;     /* Azure */
  --color-accent: 250 176 5;         /* Amber */
  --color-success: 16 185 129;       /* Teal */
  --color-warning: 234 179 8;        /* Yellow */
  --color-danger: 239 68 68;         /* Red */

  /* Neutrals (on light) */
  --color-bg: 252 252 253;
  --color-surface: 255 255 255;
  --color-muted: 247 248 250;
  --color-border: 229 231 235;
  --color-text: 17 24 39;
  --color-text-muted: 75 85 99;

  /* Radii */
  --radius-xs: 6px;
  --radius-sm: 10px;
  --radius-md: 14px;
  --radius-lg: 18px;
  --radius-xl: 26px;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0,0,0,0.06);
  --shadow-md: 0 6px 20px rgba(2, 8, 20, 0.08);
  --shadow-lg: 0 14px 40px rgba(2, 8, 20, 0.12);

  /* Spacing scale */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;
  --space-24: 96px;

  /* Typography */
  --font-sans: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Inter, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: clamp(24px, 4vw, 34px);
  --font-size-4xl: clamp(28px, 5vw, 44px);
  --font-size-5xl: clamp(32px, 6vw, 56px);

  /* Layout */
  --container-max: 1200px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-bg: 10 12 16;
    --color-surface: 17 20 26;
    --color-muted: 22 26 33;
    --color-border: 38 43 51;
    --color-text: 235 241 255;
    --color-text-muted: 177 186 199;
    --shadow-sm: 0 1px 2px rgba(0,0,0,0.6);
    --shadow-md: 0 6px 20px rgba(0,0,0,0.4);
    --shadow-lg: 0 14px 40px rgba(0,0,0,0.5);
  }
}

/* Base reset */
* { box-sizing: border-box; }
html, body { height: 100%; }
body {
  margin: 0;
  font-family: var(--font-sans);
  color: rgb(var(--color-text));
  background: rgb(var(--color-bg));
  line-height: 1.6;
}
img { max-width: 100%; display: block; }
a { color: rgb(var(--color-secondary)); text-decoration: none; }
a:hover { text-decoration: underline; }

/* Container */
.container {
  width: 100%;
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 var(--space-6);
}

/* Utilities */
.section {
  padding: var(--space-16) 0;
}
.section-title {
  font-size: var(--font-size-3xl);
  font-weight: 800;
  text-align: center;
  margin: 0 0 var(--space-8);
}
.subtle {
  color: rgb(var(--color-text-muted));
}
.grid {
  display: grid;
  gap: var(--space-6);
}
.grid-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
@media (max-width: 1024px) { .grid-4 { grid-template-columns: repeat(3, 1fr); } }
@media (max-width: 768px) { .grid-4, .grid-3 { grid-template-columns: repeat(2, 1fr); } }
@media (max-width: 520px) { .grid-4, .grid-3, .grid-2 { grid-template-columns: 1fr; } }

/* Components */
.navbar {
  position: sticky; top: 0; z-index: 20;
  background: rgba(var(--color-surface)/0.8);
  backdrop-filter: saturate(180%) blur(12px);
  border-bottom: 1px solid rgb(var(--color-border));
}
.navbar .inner { display: flex; align-items: center; justify-content: space-between; padding: var(--space-4) 0; }
.logo { display: inline-flex; align-items: center; gap: 10px; font-weight: 800; font-size: var(--font-size-lg); color: rgb(var(--color-text)); text-decoration: none; }
.logo-mark { width: 28px; height: 28px; border-radius: 8px; background: linear-gradient(135deg, rgb(var(--color-primary)) 0%, rgb(var(--color-secondary)) 100%); box-shadow: var(--shadow-md); }
.nav-links { display: flex; gap: var(--space-4); align-items: center; }
.nav-links a { padding: 8px 12px; border-radius: 10px; color: rgb(var(--color-text)); opacity: 0.9; }
.nav-links a:hover { background: rgb(var(--color-muted)); text-decoration: none; }

.btn { display: inline-flex; align-items: center; gap: 8px; border-radius: 999px; padding: 10px 16px; font-weight: 700; font-size: var(--font-size-sm); border: 1px solid transparent; transition: transform .15s ease, box-shadow .2s ease, background .2s ease, color .2s ease; cursor: pointer; text-decoration: none; }
.btn:active { transform: translateY(1px); }
.btn-primary { background: linear-gradient(135deg, rgb(var(--color-primary)) 0%, rgb(var(--color-secondary)) 100%); color: white; box-shadow: var(--shadow-md); }
.btn-primary:hover { box-shadow: var(--shadow-lg); filter: saturate(1.05); }
.btn-secondary { background: rgb(var(--color-surface)); border-color: rgb(var(--color-border)); color: rgb(var(--color-text)); }
.btn-secondary:hover { background: rgb(var(--color-muted)); }
.btn-ghost { background: transparent; color: rgb(var(--color-text)); }

.card { background: rgb(var(--color-surface)); border: 1px solid rgb(var(--color-border)); border-radius: var(--radius-lg); box-shadow: var(--shadow-sm); padding: var(--space-6); }
.card:hover { box-shadow: var(--shadow-md); }

.badge { display: inline-flex; align-items: center; gap: 6px; font-size: var(--font-size-xs); font-weight: 700; padding: 6px 10px; border-radius: 999px; border: 1px solid rgb(var(--color-border)); background: rgb(var(--color-muted)); color: rgb(var(--color-text)); }
.badge.primary { background: rgba(var(--color-primary), 0.12); color: rgb(var(--color-text)); border: 1px solid rgba(var(--color-primary), 0.18); }
.badge.success { background: rgba(var(--color-success), 0.12); border-color: rgba(var(--color-success), 0.18); }

.input { display: inline-flex; align-items: center; gap: 8px; border: 1px solid rgb(var(--color-border)); background: rgb(var(--color-surface)); color: rgb(var(--color-text)); border-radius: 12px; padding: 12px 14px; box-shadow: var(--shadow-sm); }
.input input { outline: none; border: 0; background: transparent; color: inherit; width: 100%; font-size: var(--font-size-md); }

.hero { position: relative; overflow: hidden; padding: var(--space-24) 0 var(--space-16); background: radial-gradient(1200px 600px at 20% -10%, rgba(var(--color-secondary), .18), transparent 60%), radial-gradient(1200px 600px at 80% -10%, rgba(var(--color-primary), .18), transparent 60%); }
.hero h1 { font-size: var(--font-size-5xl); line-height: 1.05; margin: 0 0 var(--space-4); letter-spacing: -0.02em; }
.hero p { font-size: var(--font-size-lg); color: rgb(var(--color-text-muted)); margin: 0 0 var(--space-6); }
.hero .actions { display: flex; gap: var(--space-4); flex-wrap: wrap; }

.deals-grid { display: grid; gap: var(--space-6); grid-template-columns: repeat(4, minmax(0, 1fr)); }
@media (max-width: 1100px) { .deals-grid { grid-template-columns: repeat(3, 1fr); } }
@media (max-width: 800px) { .deals-grid { grid-template-columns: repeat(2, 1fr); } }
@media (max-width: 520px) { .deals-grid { grid-template-columns: 1fr; } }

.deal-card { display: grid; grid-template-rows: auto 1fr auto; gap: var(--space-4); padding: var(--space-5); border-radius: var(--radius-lg); border: 1px solid rgb(var(--color-border)); background: rgb(var(--color-surface)); box-shadow: var(--shadow-sm); transition: transform .15s ease, box-shadow .2s ease; }
.deal-card:hover { transform: translateY(-3px); box-shadow: var(--shadow-md); }
.deal-media { position: relative; overflow: hidden; border-radius: 12px; background: linear-gradient(180deg, rgba(var(--color-muted),1), rgba(var(--color-muted),0.6)); aspect-ratio: 16/10; display: grid; place-items: center; }
.deal-media .logo { width: 56px; height: 56px; border-radius: 12px; background: linear-gradient(135deg, rgb(var(--color-secondary)) 0%, rgb(var(--color-primary)) 100%); box-shadow: var(--shadow-md); }
.deal-title { font-weight: 800; font-size: var(--font-size-lg); margin: 0; }
.deal-meta { display: flex; gap: 10px; align-items: center; color: rgb(var(--color-text-muted)); font-size: var(--font-size-sm); }

.carousel { display: grid; grid-auto-flow: column; grid-auto-columns: 240px; gap: var(--space-4); overflow-x: auto; scroll-snap-type: x mandatory; padding-bottom: var(--space-2); }
.carousel > * { scroll-snap-align: start; }

.footer { border-top: 1px solid rgb(var(--color-border)); padding: var(--space-12) 0; background: rgb(var(--color-surface)); }
.footer .cols { display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; gap: var(--space-8); }
@media (max-width: 900px) { .footer .cols { grid-template-columns: 1fr 1fr; } }
@media (max-width: 540px) { .footer .cols { grid-template-columns: 1fr; } }
.footer .copyright { color: rgb(var(--color-text-muted)); margin-top: var(--space-8); text-align: center; font-size: var(--font-size-sm); }
