/* Variant 1: Vibrant Gradient + Cards */
@import url('./tokens.css');

.header-cta { display: none; }
@media (min-width: 760px) { .header-cta { display: inline-flex; } }

.hero .badges { display: flex; gap: var(--space-3); flex-wrap: wrap; margin-bottom: var(--space-4); }
.hero .stats { display: grid; grid-template-columns: repeat(3, minmax(0, 1fr)); gap: var(--space-6); margin-top: var(--space-8); }
.hero .stat { background: rgb(var(--color-surface)); border: 1px solid rgb(var(--color-border)); border-radius: var(--radius-lg); padding: var(--space-6); text-align: center; box-shadow: var(--shadow-sm); }
.hero .stat .num { font-size: var(--font-size-2xl); font-weight: 900; }
.hero .stat .label { color: rgb(var(--color-text-muted)); font-size: var(--font-size-sm); }

.searchbar { margin-top: var(--space-6); max-width: 680px; }
.searchbar .input { width: 100%; }

.section.promos .grid { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.section.promos .card .row { display: flex; align-items: center; gap: var(--space-4); }
.section.promos .logo-sm { width: 36px; height: 36px; border-radius: 10px; background: linear-gradient(135deg, rgba(var(--color-secondary),0.15), rgba(var(--color-primary),0.15)); border: 1px solid rgb(var(--color-border)); }
.section.promos .offer { font-weight: 800; }

.brands .carousel .pill { border: 1px solid rgb(var(--color-border)); background: rgb(var(--color-surface)); border-radius: 999px; padding: 10px 14px; font-weight: 700; color: rgb(var(--color-text)); }

.deal-footer { display: flex; justify-content: space-between; align-items: center; }
.cashback { font-weight: 900; color: rgb(var(--color-success)); }

.testimonials { background: linear-gradient(180deg, rgba(var(--color-muted),1), rgba(var(--color-muted),0.5)); }
.quote { display: grid; gap: var(--space-3); }
.quote .who { display: flex; align-items: center; gap: 12px; color: rgb(var(--color-text-muted)); font-size: var(--font-size-sm); }
.quote .avatar { width: 36px; height: 36px; border-radius: 999px; background: linear-gradient(135deg, rgb(var(--color-secondary)), rgb(var(--color-primary))); }

.newsletter { display: grid; gap: var(--space-4); justify-items: center; text-align: center; }
.newsletter .row { display: flex; gap: var(--space-4); flex-wrap: wrap; }
