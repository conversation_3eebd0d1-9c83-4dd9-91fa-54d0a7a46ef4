/* Variant 2: <PERSON><PERSON>, Editorial, Focused on Typography */
@import url('./tokens.css');

:root { --container-max: 1000px; }

.hero { background: radial-gradient(1200px 600px at 15% -10%, rgba(var(--color-text), .06), transparent 60%); }
.hero h1 { font-weight: 900; letter-spacing: -0.03em; }
.hero p { max-width: 680px; }
.hero .actions .btn-primary { background: rgb(var(--color-text)); }
.hero .actions .btn-primary:hover { filter: brightness(1.1); }

.navbar { border: 0; background: transparent; }
.nav-links a { opacity: 0.75; }

.deal-card { border-radius: var(--radius-xl); }

.section-title { font-size: var(--font-size-4xl); letter-spacing: -0.02em; }
.subtle { font-size: var(--font-size-lg); }

.promos .card { border-radius: var(--radius-xl); }

.footer { background: transparent; }
.footer .cols a { color: rgb(var(--color-text)); opacity: .8; }
.footer .cols a:hover { opacity: 1; text-decoration: underline; }
