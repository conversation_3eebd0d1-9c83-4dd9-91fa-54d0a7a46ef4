/* Variant 3: Dark Glassmorphism + Neon Accents */
@import url('./tokens.css');

:root { --color-primary: 77 161 255; --color-secondary: 168 85 247; --color-accent: 34 197 94; }

body { background: radial-gradient(1200px 600px at 50% -10%, rgba(77, 161, 255, 0.06), transparent 60%), rgb(var(--color-bg)); }

.navbar { background: rgba(15, 17, 23, 0.6); border-bottom: 1px solid rgba(255,255,255,0.06); }
.logo-mark { background: linear-gradient(135deg, rgba(77, 161, 255, 1), rgba(168, 85, 247, 1)); box-shadow: 0 0 0 2px rgba(255,255,255,0.06), var(--shadow-md); }
.nav-links a { color: rgba(255,255,255,0.9); }

.hero { background: radial-gradient(1200px 600px at 20% -10%, rgba(77, 161, 255, 0.12), transparent 60%), radial-gradient(1200px 600px at 80% -10%, rgba(168, 85, 247, 0.12), transparent 60%); }
.hero h1 { color: rgba(255,255,255,0.98); }
.hero p { color: rgba(255,255,255,0.8); }
.btn-primary { background: linear-gradient(90deg, #4DA1FF, #A855F7); box-shadow: 0 0 0 2px rgba(255,255,255,0.06), var(--shadow-md); }

.card, .deal-card { background: rgba(255,255,255,0.06); border: 1px solid rgba(255,255,255,0.08); backdrop-filter: blur(10px); }
.badge { background: rgba(255,255,255,0.08); border-color: rgba(255,255,255,0.16); color: rgba(255,255,255,0.9); }
.subtle { color: rgba(255,255,255,0.7); }

.footer { background: rgba(255,255,255,0.04); border-top-color: rgba(255,255,255,0.06); }
.footer .cols a { color: rgba(255,255,255,0.9); }

.cashback { color: #22c55e; }
